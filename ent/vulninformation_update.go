// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/zema1/watchvuln/ent/predicate"
	"github.com/zema1/watchvuln/ent/vulninformation"
)

// VulnInformationUpdate is the builder for updating VulnInformation entities.
type VulnInformationUpdate struct {
	config
	hooks    []Hook
	mutation *VulnInformationMutation
}

// Where appends a list predicates to the VulnInformationUpdate builder.
func (viu *VulnInformationUpdate) Where(ps ...predicate.VulnInformation) *VulnInformationUpdate {
	viu.mutation.Where(ps...)
	return viu
}

// Set<PERSON><PERSON> sets the "key" field.
func (viu *VulnInformationUpdate) SetKey(s string) *VulnInformationUpdate {
	viu.mutation.SetKey(s)
	return viu
}

// SetTitle sets the "title" field.
func (viu *VulnInformationUpdate) SetTitle(s string) *VulnInformationUpdate {
	viu.mutation.SetTitle(s)
	return viu
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableTitle(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetTitle(*s)
	}
	return viu
}

// SetDescription sets the "description" field.
func (viu *VulnInformationUpdate) SetDescription(s string) *VulnInformationUpdate {
	viu.mutation.SetDescription(s)
	return viu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableDescription(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetDescription(*s)
	}
	return viu
}

// SetSeverity sets the "severity" field.
func (viu *VulnInformationUpdate) SetSeverity(s string) *VulnInformationUpdate {
	viu.mutation.SetSeverity(s)
	return viu
}

// SetNillableSeverity sets the "severity" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableSeverity(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetSeverity(*s)
	}
	return viu
}

// SetCve sets the "cve" field.
func (viu *VulnInformationUpdate) SetCve(s string) *VulnInformationUpdate {
	viu.mutation.SetCve(s)
	return viu
}

// SetNillableCve sets the "cve" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableCve(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetCve(*s)
	}
	return viu
}

// SetDisclosure sets the "disclosure" field.
func (viu *VulnInformationUpdate) SetDisclosure(s string) *VulnInformationUpdate {
	viu.mutation.SetDisclosure(s)
	return viu
}

// SetNillableDisclosure sets the "disclosure" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableDisclosure(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetDisclosure(*s)
	}
	return viu
}

// SetSolutions sets the "solutions" field.
func (viu *VulnInformationUpdate) SetSolutions(s string) *VulnInformationUpdate {
	viu.mutation.SetSolutions(s)
	return viu
}

// SetNillableSolutions sets the "solutions" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableSolutions(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetSolutions(*s)
	}
	return viu
}

// SetReferences sets the "references" field.
func (viu *VulnInformationUpdate) SetReferences(s []string) *VulnInformationUpdate {
	viu.mutation.SetReferences(s)
	return viu
}

// AppendReferences appends s to the "references" field.
func (viu *VulnInformationUpdate) AppendReferences(s []string) *VulnInformationUpdate {
	viu.mutation.AppendReferences(s)
	return viu
}

// ClearReferences clears the value of the "references" field.
func (viu *VulnInformationUpdate) ClearReferences() *VulnInformationUpdate {
	viu.mutation.ClearReferences()
	return viu
}

// SetTags sets the "tags" field.
func (viu *VulnInformationUpdate) SetTags(s []string) *VulnInformationUpdate {
	viu.mutation.SetTags(s)
	return viu
}

// AppendTags appends s to the "tags" field.
func (viu *VulnInformationUpdate) AppendTags(s []string) *VulnInformationUpdate {
	viu.mutation.AppendTags(s)
	return viu
}

// ClearTags clears the value of the "tags" field.
func (viu *VulnInformationUpdate) ClearTags() *VulnInformationUpdate {
	viu.mutation.ClearTags()
	return viu
}

// SetGithubSearch sets the "github_search" field.
func (viu *VulnInformationUpdate) SetGithubSearch(s []string) *VulnInformationUpdate {
	viu.mutation.SetGithubSearch(s)
	return viu
}

// AppendGithubSearch appends s to the "github_search" field.
func (viu *VulnInformationUpdate) AppendGithubSearch(s []string) *VulnInformationUpdate {
	viu.mutation.AppendGithubSearch(s)
	return viu
}

// ClearGithubSearch clears the value of the "github_search" field.
func (viu *VulnInformationUpdate) ClearGithubSearch() *VulnInformationUpdate {
	viu.mutation.ClearGithubSearch()
	return viu
}

// SetFrom sets the "from" field.
func (viu *VulnInformationUpdate) SetFrom(s string) *VulnInformationUpdate {
	viu.mutation.SetFrom(s)
	return viu
}

// SetNillableFrom sets the "from" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillableFrom(s *string) *VulnInformationUpdate {
	if s != nil {
		viu.SetFrom(*s)
	}
	return viu
}

// SetPushed sets the "pushed" field.
func (viu *VulnInformationUpdate) SetPushed(b bool) *VulnInformationUpdate {
	viu.mutation.SetPushed(b)
	return viu
}

// SetNillablePushed sets the "pushed" field if the given value is not nil.
func (viu *VulnInformationUpdate) SetNillablePushed(b *bool) *VulnInformationUpdate {
	if b != nil {
		viu.SetPushed(*b)
	}
	return viu
}

// SetUpdateTime sets the "update_time" field.
func (viu *VulnInformationUpdate) SetUpdateTime(t time.Time) *VulnInformationUpdate {
	viu.mutation.SetUpdateTime(t)
	return viu
}

// Mutation returns the VulnInformationMutation object of the builder.
func (viu *VulnInformationUpdate) Mutation() *VulnInformationMutation {
	return viu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (viu *VulnInformationUpdate) Save(ctx context.Context) (int, error) {
	viu.defaults()
	return withHooks(ctx, viu.sqlSave, viu.mutation, viu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (viu *VulnInformationUpdate) SaveX(ctx context.Context) int {
	affected, err := viu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (viu *VulnInformationUpdate) Exec(ctx context.Context) error {
	_, err := viu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (viu *VulnInformationUpdate) ExecX(ctx context.Context) {
	if err := viu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (viu *VulnInformationUpdate) defaults() {
	if _, ok := viu.mutation.UpdateTime(); !ok {
		v := vulninformation.UpdateDefaultUpdateTime()
		viu.mutation.SetUpdateTime(v)
	}
}

func (viu *VulnInformationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(vulninformation.Table, vulninformation.Columns, sqlgraph.NewFieldSpec(vulninformation.FieldID, field.TypeInt))
	if ps := viu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := viu.mutation.Key(); ok {
		_spec.SetField(vulninformation.FieldKey, field.TypeString, value)
	}
	if value, ok := viu.mutation.Title(); ok {
		_spec.SetField(vulninformation.FieldTitle, field.TypeString, value)
	}
	if value, ok := viu.mutation.Description(); ok {
		_spec.SetField(vulninformation.FieldDescription, field.TypeString, value)
	}
	if value, ok := viu.mutation.Severity(); ok {
		_spec.SetField(vulninformation.FieldSeverity, field.TypeString, value)
	}
	if value, ok := viu.mutation.Cve(); ok {
		_spec.SetField(vulninformation.FieldCve, field.TypeString, value)
	}
	if value, ok := viu.mutation.Disclosure(); ok {
		_spec.SetField(vulninformation.FieldDisclosure, field.TypeString, value)
	}
	if value, ok := viu.mutation.Solutions(); ok {
		_spec.SetField(vulninformation.FieldSolutions, field.TypeString, value)
	}
	if value, ok := viu.mutation.References(); ok {
		_spec.SetField(vulninformation.FieldReferences, field.TypeJSON, value)
	}
	if value, ok := viu.mutation.AppendedReferences(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldReferences, value)
		})
	}
	if viu.mutation.ReferencesCleared() {
		_spec.ClearField(vulninformation.FieldReferences, field.TypeJSON)
	}
	if value, ok := viu.mutation.Tags(); ok {
		_spec.SetField(vulninformation.FieldTags, field.TypeJSON, value)
	}
	if value, ok := viu.mutation.AppendedTags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldTags, value)
		})
	}
	if viu.mutation.TagsCleared() {
		_spec.ClearField(vulninformation.FieldTags, field.TypeJSON)
	}
	if value, ok := viu.mutation.GithubSearch(); ok {
		_spec.SetField(vulninformation.FieldGithubSearch, field.TypeJSON, value)
	}
	if value, ok := viu.mutation.AppendedGithubSearch(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldGithubSearch, value)
		})
	}
	if viu.mutation.GithubSearchCleared() {
		_spec.ClearField(vulninformation.FieldGithubSearch, field.TypeJSON)
	}
	if value, ok := viu.mutation.From(); ok {
		_spec.SetField(vulninformation.FieldFrom, field.TypeString, value)
	}
	if value, ok := viu.mutation.Pushed(); ok {
		_spec.SetField(vulninformation.FieldPushed, field.TypeBool, value)
	}
	if value, ok := viu.mutation.UpdateTime(); ok {
		_spec.SetField(vulninformation.FieldUpdateTime, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, viu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{vulninformation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	viu.mutation.done = true
	return n, nil
}

// VulnInformationUpdateOne is the builder for updating a single VulnInformation entity.
type VulnInformationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *VulnInformationMutation
}

// SetKey sets the "key" field.
func (viuo *VulnInformationUpdateOne) SetKey(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetKey(s)
	return viuo
}

// SetTitle sets the "title" field.
func (viuo *VulnInformationUpdateOne) SetTitle(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetTitle(s)
	return viuo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableTitle(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetTitle(*s)
	}
	return viuo
}

// SetDescription sets the "description" field.
func (viuo *VulnInformationUpdateOne) SetDescription(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetDescription(s)
	return viuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableDescription(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetDescription(*s)
	}
	return viuo
}

// SetSeverity sets the "severity" field.
func (viuo *VulnInformationUpdateOne) SetSeverity(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetSeverity(s)
	return viuo
}

// SetNillableSeverity sets the "severity" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableSeverity(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetSeverity(*s)
	}
	return viuo
}

// SetCve sets the "cve" field.
func (viuo *VulnInformationUpdateOne) SetCve(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetCve(s)
	return viuo
}

// SetNillableCve sets the "cve" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableCve(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetCve(*s)
	}
	return viuo
}

// SetDisclosure sets the "disclosure" field.
func (viuo *VulnInformationUpdateOne) SetDisclosure(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetDisclosure(s)
	return viuo
}

// SetNillableDisclosure sets the "disclosure" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableDisclosure(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetDisclosure(*s)
	}
	return viuo
}

// SetSolutions sets the "solutions" field.
func (viuo *VulnInformationUpdateOne) SetSolutions(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetSolutions(s)
	return viuo
}

// SetNillableSolutions sets the "solutions" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableSolutions(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetSolutions(*s)
	}
	return viuo
}

// SetReferences sets the "references" field.
func (viuo *VulnInformationUpdateOne) SetReferences(s []string) *VulnInformationUpdateOne {
	viuo.mutation.SetReferences(s)
	return viuo
}

// AppendReferences appends s to the "references" field.
func (viuo *VulnInformationUpdateOne) AppendReferences(s []string) *VulnInformationUpdateOne {
	viuo.mutation.AppendReferences(s)
	return viuo
}

// ClearReferences clears the value of the "references" field.
func (viuo *VulnInformationUpdateOne) ClearReferences() *VulnInformationUpdateOne {
	viuo.mutation.ClearReferences()
	return viuo
}

// SetTags sets the "tags" field.
func (viuo *VulnInformationUpdateOne) SetTags(s []string) *VulnInformationUpdateOne {
	viuo.mutation.SetTags(s)
	return viuo
}

// AppendTags appends s to the "tags" field.
func (viuo *VulnInformationUpdateOne) AppendTags(s []string) *VulnInformationUpdateOne {
	viuo.mutation.AppendTags(s)
	return viuo
}

// ClearTags clears the value of the "tags" field.
func (viuo *VulnInformationUpdateOne) ClearTags() *VulnInformationUpdateOne {
	viuo.mutation.ClearTags()
	return viuo
}

// SetGithubSearch sets the "github_search" field.
func (viuo *VulnInformationUpdateOne) SetGithubSearch(s []string) *VulnInformationUpdateOne {
	viuo.mutation.SetGithubSearch(s)
	return viuo
}

// AppendGithubSearch appends s to the "github_search" field.
func (viuo *VulnInformationUpdateOne) AppendGithubSearch(s []string) *VulnInformationUpdateOne {
	viuo.mutation.AppendGithubSearch(s)
	return viuo
}

// ClearGithubSearch clears the value of the "github_search" field.
func (viuo *VulnInformationUpdateOne) ClearGithubSearch() *VulnInformationUpdateOne {
	viuo.mutation.ClearGithubSearch()
	return viuo
}

// SetFrom sets the "from" field.
func (viuo *VulnInformationUpdateOne) SetFrom(s string) *VulnInformationUpdateOne {
	viuo.mutation.SetFrom(s)
	return viuo
}

// SetNillableFrom sets the "from" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillableFrom(s *string) *VulnInformationUpdateOne {
	if s != nil {
		viuo.SetFrom(*s)
	}
	return viuo
}

// SetPushed sets the "pushed" field.
func (viuo *VulnInformationUpdateOne) SetPushed(b bool) *VulnInformationUpdateOne {
	viuo.mutation.SetPushed(b)
	return viuo
}

// SetNillablePushed sets the "pushed" field if the given value is not nil.
func (viuo *VulnInformationUpdateOne) SetNillablePushed(b *bool) *VulnInformationUpdateOne {
	if b != nil {
		viuo.SetPushed(*b)
	}
	return viuo
}

// SetUpdateTime sets the "update_time" field.
func (viuo *VulnInformationUpdateOne) SetUpdateTime(t time.Time) *VulnInformationUpdateOne {
	viuo.mutation.SetUpdateTime(t)
	return viuo
}

// Mutation returns the VulnInformationMutation object of the builder.
func (viuo *VulnInformationUpdateOne) Mutation() *VulnInformationMutation {
	return viuo.mutation
}

// Where appends a list predicates to the VulnInformationUpdate builder.
func (viuo *VulnInformationUpdateOne) Where(ps ...predicate.VulnInformation) *VulnInformationUpdateOne {
	viuo.mutation.Where(ps...)
	return viuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (viuo *VulnInformationUpdateOne) Select(field string, fields ...string) *VulnInformationUpdateOne {
	viuo.fields = append([]string{field}, fields...)
	return viuo
}

// Save executes the query and returns the updated VulnInformation entity.
func (viuo *VulnInformationUpdateOne) Save(ctx context.Context) (*VulnInformation, error) {
	viuo.defaults()
	return withHooks(ctx, viuo.sqlSave, viuo.mutation, viuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (viuo *VulnInformationUpdateOne) SaveX(ctx context.Context) *VulnInformation {
	node, err := viuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (viuo *VulnInformationUpdateOne) Exec(ctx context.Context) error {
	_, err := viuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (viuo *VulnInformationUpdateOne) ExecX(ctx context.Context) {
	if err := viuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (viuo *VulnInformationUpdateOne) defaults() {
	if _, ok := viuo.mutation.UpdateTime(); !ok {
		v := vulninformation.UpdateDefaultUpdateTime()
		viuo.mutation.SetUpdateTime(v)
	}
}

func (viuo *VulnInformationUpdateOne) sqlSave(ctx context.Context) (_node *VulnInformation, err error) {
	_spec := sqlgraph.NewUpdateSpec(vulninformation.Table, vulninformation.Columns, sqlgraph.NewFieldSpec(vulninformation.FieldID, field.TypeInt))
	id, ok := viuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "VulnInformation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := viuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, vulninformation.FieldID)
		for _, f := range fields {
			if !vulninformation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != vulninformation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := viuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := viuo.mutation.Key(); ok {
		_spec.SetField(vulninformation.FieldKey, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Title(); ok {
		_spec.SetField(vulninformation.FieldTitle, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Description(); ok {
		_spec.SetField(vulninformation.FieldDescription, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Severity(); ok {
		_spec.SetField(vulninformation.FieldSeverity, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Cve(); ok {
		_spec.SetField(vulninformation.FieldCve, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Disclosure(); ok {
		_spec.SetField(vulninformation.FieldDisclosure, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Solutions(); ok {
		_spec.SetField(vulninformation.FieldSolutions, field.TypeString, value)
	}
	if value, ok := viuo.mutation.References(); ok {
		_spec.SetField(vulninformation.FieldReferences, field.TypeJSON, value)
	}
	if value, ok := viuo.mutation.AppendedReferences(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldReferences, value)
		})
	}
	if viuo.mutation.ReferencesCleared() {
		_spec.ClearField(vulninformation.FieldReferences, field.TypeJSON)
	}
	if value, ok := viuo.mutation.Tags(); ok {
		_spec.SetField(vulninformation.FieldTags, field.TypeJSON, value)
	}
	if value, ok := viuo.mutation.AppendedTags(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldTags, value)
		})
	}
	if viuo.mutation.TagsCleared() {
		_spec.ClearField(vulninformation.FieldTags, field.TypeJSON)
	}
	if value, ok := viuo.mutation.GithubSearch(); ok {
		_spec.SetField(vulninformation.FieldGithubSearch, field.TypeJSON, value)
	}
	if value, ok := viuo.mutation.AppendedGithubSearch(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, vulninformation.FieldGithubSearch, value)
		})
	}
	if viuo.mutation.GithubSearchCleared() {
		_spec.ClearField(vulninformation.FieldGithubSearch, field.TypeJSON)
	}
	if value, ok := viuo.mutation.From(); ok {
		_spec.SetField(vulninformation.FieldFrom, field.TypeString, value)
	}
	if value, ok := viuo.mutation.Pushed(); ok {
		_spec.SetField(vulninformation.FieldPushed, field.TypeBool, value)
	}
	if value, ok := viuo.mutation.UpdateTime(); ok {
		_spec.SetField(vulninformation.FieldUpdateTime, field.TypeTime, value)
	}
	_node = &VulnInformation{config: viuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, viuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{vulninformation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	viuo.mutation.done = true
	return _node, nil
}
