// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/zema1/watchvuln/ent/predicate"
	"github.com/zema1/watchvuln/ent/vulninformation"
)

// VulnInformationDelete is the builder for deleting a VulnInformation entity.
type VulnInformationDelete struct {
	config
	hooks    []Hook
	mutation *VulnInformationMutation
}

// Where appends a list predicates to the VulnInformationDelete builder.
func (vid *VulnInformationDelete) Where(ps ...predicate.VulnInformation) *VulnInformationDelete {
	vid.mutation.Where(ps...)
	return vid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (vid *VulnInformationDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, vid.sqlExec, vid.mutation, vid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (vid *VulnInformationDelete) ExecX(ctx context.Context) int {
	n, err := vid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (vid *VulnInformationDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(vulninformation.Table, sqlgraph.NewFieldSpec(vulninformation.FieldID, field.TypeInt))
	if ps := vid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, vid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	vid.mutation.done = true
	return affected, err
}

// VulnInformationDeleteOne is the builder for deleting a single VulnInformation entity.
type VulnInformationDeleteOne struct {
	vid *VulnInformationDelete
}

// Where appends a list predicates to the VulnInformationDelete builder.
func (vido *VulnInformationDeleteOne) Where(ps ...predicate.VulnInformation) *VulnInformationDeleteOne {
	vido.vid.mutation.Where(ps...)
	return vido
}

// Exec executes the deletion query.
func (vido *VulnInformationDeleteOne) Exec(ctx context.Context) error {
	n, err := vido.vid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{vulninformation.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (vido *VulnInformationDeleteOne) ExecX(ctx context.Context) {
	if err := vido.Exec(ctx); err != nil {
		panic(err)
	}
}
