// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/zema1/watchvuln/ent/vulninformation"
)

// VulnInformationCreate is the builder for creating a VulnInformation entity.
type VulnInformationCreate struct {
	config
	mutation *VulnInformationMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetKey sets the "key" field.
func (vic *VulnInformationCreate) SetKey(s string) *VulnInformationCreate {
	vic.mutation.SetKey(s)
	return vic
}

// SetTitle sets the "title" field.
func (vic *VulnInformationCreate) SetTitle(s string) *VulnInformationCreate {
	vic.mutation.SetTitle(s)
	return vic
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableTitle(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetTitle(*s)
	}
	return vic
}

// SetDescription sets the "description" field.
func (vic *VulnInformationCreate) SetDescription(s string) *VulnInformationCreate {
	vic.mutation.SetDescription(s)
	return vic
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableDescription(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetDescription(*s)
	}
	return vic
}

// SetSeverity sets the "severity" field.
func (vic *VulnInformationCreate) SetSeverity(s string) *VulnInformationCreate {
	vic.mutation.SetSeverity(s)
	return vic
}

// SetNillableSeverity sets the "severity" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableSeverity(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetSeverity(*s)
	}
	return vic
}

// SetCve sets the "cve" field.
func (vic *VulnInformationCreate) SetCve(s string) *VulnInformationCreate {
	vic.mutation.SetCve(s)
	return vic
}

// SetNillableCve sets the "cve" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableCve(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetCve(*s)
	}
	return vic
}

// SetDisclosure sets the "disclosure" field.
func (vic *VulnInformationCreate) SetDisclosure(s string) *VulnInformationCreate {
	vic.mutation.SetDisclosure(s)
	return vic
}

// SetNillableDisclosure sets the "disclosure" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableDisclosure(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetDisclosure(*s)
	}
	return vic
}

// SetSolutions sets the "solutions" field.
func (vic *VulnInformationCreate) SetSolutions(s string) *VulnInformationCreate {
	vic.mutation.SetSolutions(s)
	return vic
}

// SetNillableSolutions sets the "solutions" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableSolutions(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetSolutions(*s)
	}
	return vic
}

// SetReferences sets the "references" field.
func (vic *VulnInformationCreate) SetReferences(s []string) *VulnInformationCreate {
	vic.mutation.SetReferences(s)
	return vic
}

// SetTags sets the "tags" field.
func (vic *VulnInformationCreate) SetTags(s []string) *VulnInformationCreate {
	vic.mutation.SetTags(s)
	return vic
}

// SetGithubSearch sets the "github_search" field.
func (vic *VulnInformationCreate) SetGithubSearch(s []string) *VulnInformationCreate {
	vic.mutation.SetGithubSearch(s)
	return vic
}

// SetFrom sets the "from" field.
func (vic *VulnInformationCreate) SetFrom(s string) *VulnInformationCreate {
	vic.mutation.SetFrom(s)
	return vic
}

// SetNillableFrom sets the "from" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableFrom(s *string) *VulnInformationCreate {
	if s != nil {
		vic.SetFrom(*s)
	}
	return vic
}

// SetPushed sets the "pushed" field.
func (vic *VulnInformationCreate) SetPushed(b bool) *VulnInformationCreate {
	vic.mutation.SetPushed(b)
	return vic
}

// SetNillablePushed sets the "pushed" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillablePushed(b *bool) *VulnInformationCreate {
	if b != nil {
		vic.SetPushed(*b)
	}
	return vic
}

// SetCreateTime sets the "create_time" field.
func (vic *VulnInformationCreate) SetCreateTime(t time.Time) *VulnInformationCreate {
	vic.mutation.SetCreateTime(t)
	return vic
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableCreateTime(t *time.Time) *VulnInformationCreate {
	if t != nil {
		vic.SetCreateTime(*t)
	}
	return vic
}

// SetUpdateTime sets the "update_time" field.
func (vic *VulnInformationCreate) SetUpdateTime(t time.Time) *VulnInformationCreate {
	vic.mutation.SetUpdateTime(t)
	return vic
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (vic *VulnInformationCreate) SetNillableUpdateTime(t *time.Time) *VulnInformationCreate {
	if t != nil {
		vic.SetUpdateTime(*t)
	}
	return vic
}

// Mutation returns the VulnInformationMutation object of the builder.
func (vic *VulnInformationCreate) Mutation() *VulnInformationMutation {
	return vic.mutation
}

// Save creates the VulnInformation in the database.
func (vic *VulnInformationCreate) Save(ctx context.Context) (*VulnInformation, error) {
	vic.defaults()
	return withHooks(ctx, vic.sqlSave, vic.mutation, vic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (vic *VulnInformationCreate) SaveX(ctx context.Context) *VulnInformation {
	v, err := vic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (vic *VulnInformationCreate) Exec(ctx context.Context) error {
	_, err := vic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vic *VulnInformationCreate) ExecX(ctx context.Context) {
	if err := vic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (vic *VulnInformationCreate) defaults() {
	if _, ok := vic.mutation.Title(); !ok {
		v := vulninformation.DefaultTitle
		vic.mutation.SetTitle(v)
	}
	if _, ok := vic.mutation.Description(); !ok {
		v := vulninformation.DefaultDescription
		vic.mutation.SetDescription(v)
	}
	if _, ok := vic.mutation.Severity(); !ok {
		v := vulninformation.DefaultSeverity
		vic.mutation.SetSeverity(v)
	}
	if _, ok := vic.mutation.Cve(); !ok {
		v := vulninformation.DefaultCve
		vic.mutation.SetCve(v)
	}
	if _, ok := vic.mutation.Disclosure(); !ok {
		v := vulninformation.DefaultDisclosure
		vic.mutation.SetDisclosure(v)
	}
	if _, ok := vic.mutation.Solutions(); !ok {
		v := vulninformation.DefaultSolutions
		vic.mutation.SetSolutions(v)
	}
	if _, ok := vic.mutation.From(); !ok {
		v := vulninformation.DefaultFrom
		vic.mutation.SetFrom(v)
	}
	if _, ok := vic.mutation.Pushed(); !ok {
		v := vulninformation.DefaultPushed
		vic.mutation.SetPushed(v)
	}
	if _, ok := vic.mutation.CreateTime(); !ok {
		v := vulninformation.DefaultCreateTime()
		vic.mutation.SetCreateTime(v)
	}
	if _, ok := vic.mutation.UpdateTime(); !ok {
		v := vulninformation.DefaultUpdateTime()
		vic.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (vic *VulnInformationCreate) check() error {
	if _, ok := vic.mutation.Key(); !ok {
		return &ValidationError{Name: "key", err: errors.New(`ent: missing required field "VulnInformation.key"`)}
	}
	if _, ok := vic.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "VulnInformation.title"`)}
	}
	if _, ok := vic.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "VulnInformation.description"`)}
	}
	if _, ok := vic.mutation.Severity(); !ok {
		return &ValidationError{Name: "severity", err: errors.New(`ent: missing required field "VulnInformation.severity"`)}
	}
	if _, ok := vic.mutation.Cve(); !ok {
		return &ValidationError{Name: "cve", err: errors.New(`ent: missing required field "VulnInformation.cve"`)}
	}
	if _, ok := vic.mutation.Disclosure(); !ok {
		return &ValidationError{Name: "disclosure", err: errors.New(`ent: missing required field "VulnInformation.disclosure"`)}
	}
	if _, ok := vic.mutation.Solutions(); !ok {
		return &ValidationError{Name: "solutions", err: errors.New(`ent: missing required field "VulnInformation.solutions"`)}
	}
	if _, ok := vic.mutation.From(); !ok {
		return &ValidationError{Name: "from", err: errors.New(`ent: missing required field "VulnInformation.from"`)}
	}
	if _, ok := vic.mutation.Pushed(); !ok {
		return &ValidationError{Name: "pushed", err: errors.New(`ent: missing required field "VulnInformation.pushed"`)}
	}
	if _, ok := vic.mutation.CreateTime(); !ok {
		return &ValidationError{Name: "create_time", err: errors.New(`ent: missing required field "VulnInformation.create_time"`)}
	}
	if _, ok := vic.mutation.UpdateTime(); !ok {
		return &ValidationError{Name: "update_time", err: errors.New(`ent: missing required field "VulnInformation.update_time"`)}
	}
	return nil
}

func (vic *VulnInformationCreate) sqlSave(ctx context.Context) (*VulnInformation, error) {
	if err := vic.check(); err != nil {
		return nil, err
	}
	_node, _spec := vic.createSpec()
	if err := sqlgraph.CreateNode(ctx, vic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	vic.mutation.id = &_node.ID
	vic.mutation.done = true
	return _node, nil
}

func (vic *VulnInformationCreate) createSpec() (*VulnInformation, *sqlgraph.CreateSpec) {
	var (
		_node = &VulnInformation{config: vic.config}
		_spec = sqlgraph.NewCreateSpec(vulninformation.Table, sqlgraph.NewFieldSpec(vulninformation.FieldID, field.TypeInt))
	)
	_spec.OnConflict = vic.conflict
	if value, ok := vic.mutation.Key(); ok {
		_spec.SetField(vulninformation.FieldKey, field.TypeString, value)
		_node.Key = value
	}
	if value, ok := vic.mutation.Title(); ok {
		_spec.SetField(vulninformation.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := vic.mutation.Description(); ok {
		_spec.SetField(vulninformation.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := vic.mutation.Severity(); ok {
		_spec.SetField(vulninformation.FieldSeverity, field.TypeString, value)
		_node.Severity = value
	}
	if value, ok := vic.mutation.Cve(); ok {
		_spec.SetField(vulninformation.FieldCve, field.TypeString, value)
		_node.Cve = value
	}
	if value, ok := vic.mutation.Disclosure(); ok {
		_spec.SetField(vulninformation.FieldDisclosure, field.TypeString, value)
		_node.Disclosure = value
	}
	if value, ok := vic.mutation.Solutions(); ok {
		_spec.SetField(vulninformation.FieldSolutions, field.TypeString, value)
		_node.Solutions = value
	}
	if value, ok := vic.mutation.References(); ok {
		_spec.SetField(vulninformation.FieldReferences, field.TypeJSON, value)
		_node.References = value
	}
	if value, ok := vic.mutation.Tags(); ok {
		_spec.SetField(vulninformation.FieldTags, field.TypeJSON, value)
		_node.Tags = value
	}
	if value, ok := vic.mutation.GithubSearch(); ok {
		_spec.SetField(vulninformation.FieldGithubSearch, field.TypeJSON, value)
		_node.GithubSearch = value
	}
	if value, ok := vic.mutation.From(); ok {
		_spec.SetField(vulninformation.FieldFrom, field.TypeString, value)
		_node.From = value
	}
	if value, ok := vic.mutation.Pushed(); ok {
		_spec.SetField(vulninformation.FieldPushed, field.TypeBool, value)
		_node.Pushed = value
	}
	if value, ok := vic.mutation.CreateTime(); ok {
		_spec.SetField(vulninformation.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := vic.mutation.UpdateTime(); ok {
		_spec.SetField(vulninformation.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.VulnInformation.Create().
//		SetKey(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.VulnInformationUpsert) {
//			SetKey(v+v).
//		}).
//		Exec(ctx)
func (vic *VulnInformationCreate) OnConflict(opts ...sql.ConflictOption) *VulnInformationUpsertOne {
	vic.conflict = opts
	return &VulnInformationUpsertOne{
		create: vic,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (vic *VulnInformationCreate) OnConflictColumns(columns ...string) *VulnInformationUpsertOne {
	vic.conflict = append(vic.conflict, sql.ConflictColumns(columns...))
	return &VulnInformationUpsertOne{
		create: vic,
	}
}

type (
	// VulnInformationUpsertOne is the builder for "upsert"-ing
	//  one VulnInformation node.
	VulnInformationUpsertOne struct {
		create *VulnInformationCreate
	}

	// VulnInformationUpsert is the "OnConflict" setter.
	VulnInformationUpsert struct {
		*sql.UpdateSet
	}
)

// SetKey sets the "key" field.
func (u *VulnInformationUpsert) SetKey(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldKey, v)
	return u
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateKey() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldKey)
	return u
}

// SetTitle sets the "title" field.
func (u *VulnInformationUpsert) SetTitle(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldTitle, v)
	return u
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateTitle() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldTitle)
	return u
}

// SetDescription sets the "description" field.
func (u *VulnInformationUpsert) SetDescription(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldDescription, v)
	return u
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateDescription() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldDescription)
	return u
}

// SetSeverity sets the "severity" field.
func (u *VulnInformationUpsert) SetSeverity(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldSeverity, v)
	return u
}

// UpdateSeverity sets the "severity" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateSeverity() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldSeverity)
	return u
}

// SetCve sets the "cve" field.
func (u *VulnInformationUpsert) SetCve(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldCve, v)
	return u
}

// UpdateCve sets the "cve" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateCve() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldCve)
	return u
}

// SetDisclosure sets the "disclosure" field.
func (u *VulnInformationUpsert) SetDisclosure(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldDisclosure, v)
	return u
}

// UpdateDisclosure sets the "disclosure" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateDisclosure() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldDisclosure)
	return u
}

// SetSolutions sets the "solutions" field.
func (u *VulnInformationUpsert) SetSolutions(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldSolutions, v)
	return u
}

// UpdateSolutions sets the "solutions" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateSolutions() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldSolutions)
	return u
}

// SetReferences sets the "references" field.
func (u *VulnInformationUpsert) SetReferences(v []string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldReferences, v)
	return u
}

// UpdateReferences sets the "references" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateReferences() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldReferences)
	return u
}

// ClearReferences clears the value of the "references" field.
func (u *VulnInformationUpsert) ClearReferences() *VulnInformationUpsert {
	u.SetNull(vulninformation.FieldReferences)
	return u
}

// SetTags sets the "tags" field.
func (u *VulnInformationUpsert) SetTags(v []string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldTags, v)
	return u
}

// UpdateTags sets the "tags" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateTags() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldTags)
	return u
}

// ClearTags clears the value of the "tags" field.
func (u *VulnInformationUpsert) ClearTags() *VulnInformationUpsert {
	u.SetNull(vulninformation.FieldTags)
	return u
}

// SetGithubSearch sets the "github_search" field.
func (u *VulnInformationUpsert) SetGithubSearch(v []string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldGithubSearch, v)
	return u
}

// UpdateGithubSearch sets the "github_search" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateGithubSearch() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldGithubSearch)
	return u
}

// ClearGithubSearch clears the value of the "github_search" field.
func (u *VulnInformationUpsert) ClearGithubSearch() *VulnInformationUpsert {
	u.SetNull(vulninformation.FieldGithubSearch)
	return u
}

// SetFrom sets the "from" field.
func (u *VulnInformationUpsert) SetFrom(v string) *VulnInformationUpsert {
	u.Set(vulninformation.FieldFrom, v)
	return u
}

// UpdateFrom sets the "from" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateFrom() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldFrom)
	return u
}

// SetPushed sets the "pushed" field.
func (u *VulnInformationUpsert) SetPushed(v bool) *VulnInformationUpsert {
	u.Set(vulninformation.FieldPushed, v)
	return u
}

// UpdatePushed sets the "pushed" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdatePushed() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldPushed)
	return u
}

// SetUpdateTime sets the "update_time" field.
func (u *VulnInformationUpsert) SetUpdateTime(v time.Time) *VulnInformationUpsert {
	u.Set(vulninformation.FieldUpdateTime, v)
	return u
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *VulnInformationUpsert) UpdateUpdateTime() *VulnInformationUpsert {
	u.SetExcluded(vulninformation.FieldUpdateTime)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create.
// Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *VulnInformationUpsertOne) UpdateNewValues() *VulnInformationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(vulninformation.FieldCreateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *VulnInformationUpsertOne) Ignore() *VulnInformationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *VulnInformationUpsertOne) DoNothing() *VulnInformationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the VulnInformationCreate.OnConflict
// documentation for more info.
func (u *VulnInformationUpsertOne) Update(set func(*VulnInformationUpsert)) *VulnInformationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&VulnInformationUpsert{UpdateSet: update})
	}))
	return u
}

// SetKey sets the "key" field.
func (u *VulnInformationUpsertOne) SetKey(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateKey() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateKey()
	})
}

// SetTitle sets the "title" field.
func (u *VulnInformationUpsertOne) SetTitle(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateTitle() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateTitle()
	})
}

// SetDescription sets the "description" field.
func (u *VulnInformationUpsertOne) SetDescription(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateDescription() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateDescription()
	})
}

// SetSeverity sets the "severity" field.
func (u *VulnInformationUpsertOne) SetSeverity(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetSeverity(v)
	})
}

// UpdateSeverity sets the "severity" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateSeverity() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateSeverity()
	})
}

// SetCve sets the "cve" field.
func (u *VulnInformationUpsertOne) SetCve(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetCve(v)
	})
}

// UpdateCve sets the "cve" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateCve() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateCve()
	})
}

// SetDisclosure sets the "disclosure" field.
func (u *VulnInformationUpsertOne) SetDisclosure(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetDisclosure(v)
	})
}

// UpdateDisclosure sets the "disclosure" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateDisclosure() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateDisclosure()
	})
}

// SetSolutions sets the "solutions" field.
func (u *VulnInformationUpsertOne) SetSolutions(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetSolutions(v)
	})
}

// UpdateSolutions sets the "solutions" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateSolutions() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateSolutions()
	})
}

// SetReferences sets the "references" field.
func (u *VulnInformationUpsertOne) SetReferences(v []string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetReferences(v)
	})
}

// UpdateReferences sets the "references" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateReferences() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateReferences()
	})
}

// ClearReferences clears the value of the "references" field.
func (u *VulnInformationUpsertOne) ClearReferences() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearReferences()
	})
}

// SetTags sets the "tags" field.
func (u *VulnInformationUpsertOne) SetTags(v []string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetTags(v)
	})
}

// UpdateTags sets the "tags" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateTags() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateTags()
	})
}

// ClearTags clears the value of the "tags" field.
func (u *VulnInformationUpsertOne) ClearTags() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearTags()
	})
}

// SetGithubSearch sets the "github_search" field.
func (u *VulnInformationUpsertOne) SetGithubSearch(v []string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetGithubSearch(v)
	})
}

// UpdateGithubSearch sets the "github_search" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateGithubSearch() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateGithubSearch()
	})
}

// ClearGithubSearch clears the value of the "github_search" field.
func (u *VulnInformationUpsertOne) ClearGithubSearch() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearGithubSearch()
	})
}

// SetFrom sets the "from" field.
func (u *VulnInformationUpsertOne) SetFrom(v string) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetFrom(v)
	})
}

// UpdateFrom sets the "from" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateFrom() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateFrom()
	})
}

// SetPushed sets the "pushed" field.
func (u *VulnInformationUpsertOne) SetPushed(v bool) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetPushed(v)
	})
}

// UpdatePushed sets the "pushed" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdatePushed() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdatePushed()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *VulnInformationUpsertOne) SetUpdateTime(v time.Time) *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *VulnInformationUpsertOne) UpdateUpdateTime() *VulnInformationUpsertOne {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateUpdateTime()
	})
}

// Exec executes the query.
func (u *VulnInformationUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for VulnInformationCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *VulnInformationUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *VulnInformationUpsertOne) ID(ctx context.Context) (id int, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *VulnInformationUpsertOne) IDX(ctx context.Context) int {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// VulnInformationCreateBulk is the builder for creating many VulnInformation entities in bulk.
type VulnInformationCreateBulk struct {
	config
	err      error
	builders []*VulnInformationCreate
	conflict []sql.ConflictOption
}

// Save creates the VulnInformation entities in the database.
func (vicb *VulnInformationCreateBulk) Save(ctx context.Context) ([]*VulnInformation, error) {
	if vicb.err != nil {
		return nil, vicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(vicb.builders))
	nodes := make([]*VulnInformation, len(vicb.builders))
	mutators := make([]Mutator, len(vicb.builders))
	for i := range vicb.builders {
		func(i int, root context.Context) {
			builder := vicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*VulnInformationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, vicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = vicb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, vicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, vicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (vicb *VulnInformationCreateBulk) SaveX(ctx context.Context) []*VulnInformation {
	v, err := vicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (vicb *VulnInformationCreateBulk) Exec(ctx context.Context) error {
	_, err := vicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vicb *VulnInformationCreateBulk) ExecX(ctx context.Context) {
	if err := vicb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.VulnInformation.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.VulnInformationUpsert) {
//			SetKey(v+v).
//		}).
//		Exec(ctx)
func (vicb *VulnInformationCreateBulk) OnConflict(opts ...sql.ConflictOption) *VulnInformationUpsertBulk {
	vicb.conflict = opts
	return &VulnInformationUpsertBulk{
		create: vicb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (vicb *VulnInformationCreateBulk) OnConflictColumns(columns ...string) *VulnInformationUpsertBulk {
	vicb.conflict = append(vicb.conflict, sql.ConflictColumns(columns...))
	return &VulnInformationUpsertBulk{
		create: vicb,
	}
}

// VulnInformationUpsertBulk is the builder for "upsert"-ing
// a bulk of VulnInformation nodes.
type VulnInformationUpsertBulk struct {
	create *VulnInformationCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *VulnInformationUpsertBulk) UpdateNewValues() *VulnInformationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(vulninformation.FieldCreateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.VulnInformation.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *VulnInformationUpsertBulk) Ignore() *VulnInformationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *VulnInformationUpsertBulk) DoNothing() *VulnInformationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the VulnInformationCreateBulk.OnConflict
// documentation for more info.
func (u *VulnInformationUpsertBulk) Update(set func(*VulnInformationUpsert)) *VulnInformationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&VulnInformationUpsert{UpdateSet: update})
	}))
	return u
}

// SetKey sets the "key" field.
func (u *VulnInformationUpsertBulk) SetKey(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateKey() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateKey()
	})
}

// SetTitle sets the "title" field.
func (u *VulnInformationUpsertBulk) SetTitle(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateTitle() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateTitle()
	})
}

// SetDescription sets the "description" field.
func (u *VulnInformationUpsertBulk) SetDescription(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateDescription() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateDescription()
	})
}

// SetSeverity sets the "severity" field.
func (u *VulnInformationUpsertBulk) SetSeverity(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetSeverity(v)
	})
}

// UpdateSeverity sets the "severity" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateSeverity() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateSeverity()
	})
}

// SetCve sets the "cve" field.
func (u *VulnInformationUpsertBulk) SetCve(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetCve(v)
	})
}

// UpdateCve sets the "cve" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateCve() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateCve()
	})
}

// SetDisclosure sets the "disclosure" field.
func (u *VulnInformationUpsertBulk) SetDisclosure(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetDisclosure(v)
	})
}

// UpdateDisclosure sets the "disclosure" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateDisclosure() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateDisclosure()
	})
}

// SetSolutions sets the "solutions" field.
func (u *VulnInformationUpsertBulk) SetSolutions(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetSolutions(v)
	})
}

// UpdateSolutions sets the "solutions" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateSolutions() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateSolutions()
	})
}

// SetReferences sets the "references" field.
func (u *VulnInformationUpsertBulk) SetReferences(v []string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetReferences(v)
	})
}

// UpdateReferences sets the "references" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateReferences() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateReferences()
	})
}

// ClearReferences clears the value of the "references" field.
func (u *VulnInformationUpsertBulk) ClearReferences() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearReferences()
	})
}

// SetTags sets the "tags" field.
func (u *VulnInformationUpsertBulk) SetTags(v []string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetTags(v)
	})
}

// UpdateTags sets the "tags" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateTags() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateTags()
	})
}

// ClearTags clears the value of the "tags" field.
func (u *VulnInformationUpsertBulk) ClearTags() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearTags()
	})
}

// SetGithubSearch sets the "github_search" field.
func (u *VulnInformationUpsertBulk) SetGithubSearch(v []string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetGithubSearch(v)
	})
}

// UpdateGithubSearch sets the "github_search" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateGithubSearch() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateGithubSearch()
	})
}

// ClearGithubSearch clears the value of the "github_search" field.
func (u *VulnInformationUpsertBulk) ClearGithubSearch() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.ClearGithubSearch()
	})
}

// SetFrom sets the "from" field.
func (u *VulnInformationUpsertBulk) SetFrom(v string) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetFrom(v)
	})
}

// UpdateFrom sets the "from" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateFrom() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateFrom()
	})
}

// SetPushed sets the "pushed" field.
func (u *VulnInformationUpsertBulk) SetPushed(v bool) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetPushed(v)
	})
}

// UpdatePushed sets the "pushed" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdatePushed() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdatePushed()
	})
}

// SetUpdateTime sets the "update_time" field.
func (u *VulnInformationUpsertBulk) SetUpdateTime(v time.Time) *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.SetUpdateTime(v)
	})
}

// UpdateUpdateTime sets the "update_time" field to the value that was provided on create.
func (u *VulnInformationUpsertBulk) UpdateUpdateTime() *VulnInformationUpsertBulk {
	return u.Update(func(s *VulnInformationUpsert) {
		s.UpdateUpdateTime()
	})
}

// Exec executes the query.
func (u *VulnInformationUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the VulnInformationCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for VulnInformationCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *VulnInformationUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
