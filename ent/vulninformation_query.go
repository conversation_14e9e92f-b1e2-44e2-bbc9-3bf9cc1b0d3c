// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/zema1/watchvuln/ent/predicate"
	"github.com/zema1/watchvuln/ent/vulninformation"
)

// VulnInformationQuery is the builder for querying VulnInformation entities.
type VulnInformationQuery struct {
	config
	ctx        *QueryContext
	order      []vulninformation.OrderOption
	inters     []Interceptor
	predicates []predicate.VulnInformation
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the VulnInformationQuery builder.
func (viq *VulnInformationQuery) Where(ps ...predicate.VulnInformation) *VulnInformationQuery {
	viq.predicates = append(viq.predicates, ps...)
	return viq
}

// Limit the number of records to be returned by this query.
func (viq *VulnInformationQuery) Limit(limit int) *VulnInformationQuery {
	viq.ctx.Limit = &limit
	return viq
}

// Offset to start from.
func (viq *VulnInformationQuery) Offset(offset int) *VulnInformationQuery {
	viq.ctx.Offset = &offset
	return viq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (viq *VulnInformationQuery) Unique(unique bool) *VulnInformationQuery {
	viq.ctx.Unique = &unique
	return viq
}

// Order specifies how the records should be ordered.
func (viq *VulnInformationQuery) Order(o ...vulninformation.OrderOption) *VulnInformationQuery {
	viq.order = append(viq.order, o...)
	return viq
}

// First returns the first VulnInformation entity from the query.
// Returns a *NotFoundError when no VulnInformation was found.
func (viq *VulnInformationQuery) First(ctx context.Context) (*VulnInformation, error) {
	nodes, err := viq.Limit(1).All(setContextOp(ctx, viq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{vulninformation.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (viq *VulnInformationQuery) FirstX(ctx context.Context) *VulnInformation {
	node, err := viq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first VulnInformation ID from the query.
// Returns a *NotFoundError when no VulnInformation ID was found.
func (viq *VulnInformationQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = viq.Limit(1).IDs(setContextOp(ctx, viq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{vulninformation.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (viq *VulnInformationQuery) FirstIDX(ctx context.Context) int {
	id, err := viq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single VulnInformation entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one VulnInformation entity is found.
// Returns a *NotFoundError when no VulnInformation entities are found.
func (viq *VulnInformationQuery) Only(ctx context.Context) (*VulnInformation, error) {
	nodes, err := viq.Limit(2).All(setContextOp(ctx, viq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{vulninformation.Label}
	default:
		return nil, &NotSingularError{vulninformation.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (viq *VulnInformationQuery) OnlyX(ctx context.Context) *VulnInformation {
	node, err := viq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only VulnInformation ID in the query.
// Returns a *NotSingularError when more than one VulnInformation ID is found.
// Returns a *NotFoundError when no entities are found.
func (viq *VulnInformationQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = viq.Limit(2).IDs(setContextOp(ctx, viq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{vulninformation.Label}
	default:
		err = &NotSingularError{vulninformation.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (viq *VulnInformationQuery) OnlyIDX(ctx context.Context) int {
	id, err := viq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of VulnInformations.
func (viq *VulnInformationQuery) All(ctx context.Context) ([]*VulnInformation, error) {
	ctx = setContextOp(ctx, viq.ctx, "All")
	if err := viq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*VulnInformation, *VulnInformationQuery]()
	return withInterceptors[[]*VulnInformation](ctx, viq, qr, viq.inters)
}

// AllX is like All, but panics if an error occurs.
func (viq *VulnInformationQuery) AllX(ctx context.Context) []*VulnInformation {
	nodes, err := viq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of VulnInformation IDs.
func (viq *VulnInformationQuery) IDs(ctx context.Context) (ids []int, err error) {
	if viq.ctx.Unique == nil && viq.path != nil {
		viq.Unique(true)
	}
	ctx = setContextOp(ctx, viq.ctx, "IDs")
	if err = viq.Select(vulninformation.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (viq *VulnInformationQuery) IDsX(ctx context.Context) []int {
	ids, err := viq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (viq *VulnInformationQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, viq.ctx, "Count")
	if err := viq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, viq, querierCount[*VulnInformationQuery](), viq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (viq *VulnInformationQuery) CountX(ctx context.Context) int {
	count, err := viq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (viq *VulnInformationQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, viq.ctx, "Exist")
	switch _, err := viq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (viq *VulnInformationQuery) ExistX(ctx context.Context) bool {
	exist, err := viq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the VulnInformationQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (viq *VulnInformationQuery) Clone() *VulnInformationQuery {
	if viq == nil {
		return nil
	}
	return &VulnInformationQuery{
		config:     viq.config,
		ctx:        viq.ctx.Clone(),
		order:      append([]vulninformation.OrderOption{}, viq.order...),
		inters:     append([]Interceptor{}, viq.inters...),
		predicates: append([]predicate.VulnInformation{}, viq.predicates...),
		// clone intermediate query.
		sql:  viq.sql.Clone(),
		path: viq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Key string `json:"key,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.VulnInformation.Query().
//		GroupBy(vulninformation.FieldKey).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (viq *VulnInformationQuery) GroupBy(field string, fields ...string) *VulnInformationGroupBy {
	viq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &VulnInformationGroupBy{build: viq}
	grbuild.flds = &viq.ctx.Fields
	grbuild.label = vulninformation.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Key string `json:"key,omitempty"`
//	}
//
//	client.VulnInformation.Query().
//		Select(vulninformation.FieldKey).
//		Scan(ctx, &v)
func (viq *VulnInformationQuery) Select(fields ...string) *VulnInformationSelect {
	viq.ctx.Fields = append(viq.ctx.Fields, fields...)
	sbuild := &VulnInformationSelect{VulnInformationQuery: viq}
	sbuild.label = vulninformation.Label
	sbuild.flds, sbuild.scan = &viq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a VulnInformationSelect configured with the given aggregations.
func (viq *VulnInformationQuery) Aggregate(fns ...AggregateFunc) *VulnInformationSelect {
	return viq.Select().Aggregate(fns...)
}

func (viq *VulnInformationQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range viq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, viq); err != nil {
				return err
			}
		}
	}
	for _, f := range viq.ctx.Fields {
		if !vulninformation.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if viq.path != nil {
		prev, err := viq.path(ctx)
		if err != nil {
			return err
		}
		viq.sql = prev
	}
	return nil
}

func (viq *VulnInformationQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*VulnInformation, error) {
	var (
		nodes = []*VulnInformation{}
		_spec = viq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*VulnInformation).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &VulnInformation{config: viq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, viq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (viq *VulnInformationQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := viq.querySpec()
	_spec.Node.Columns = viq.ctx.Fields
	if len(viq.ctx.Fields) > 0 {
		_spec.Unique = viq.ctx.Unique != nil && *viq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, viq.driver, _spec)
}

func (viq *VulnInformationQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(vulninformation.Table, vulninformation.Columns, sqlgraph.NewFieldSpec(vulninformation.FieldID, field.TypeInt))
	_spec.From = viq.sql
	if unique := viq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if viq.path != nil {
		_spec.Unique = true
	}
	if fields := viq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, vulninformation.FieldID)
		for i := range fields {
			if fields[i] != vulninformation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := viq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := viq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := viq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := viq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (viq *VulnInformationQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(viq.driver.Dialect())
	t1 := builder.Table(vulninformation.Table)
	columns := viq.ctx.Fields
	if len(columns) == 0 {
		columns = vulninformation.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if viq.sql != nil {
		selector = viq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if viq.ctx.Unique != nil && *viq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range viq.predicates {
		p(selector)
	}
	for _, p := range viq.order {
		p(selector)
	}
	if offset := viq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := viq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// VulnInformationGroupBy is the group-by builder for VulnInformation entities.
type VulnInformationGroupBy struct {
	selector
	build *VulnInformationQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (vigb *VulnInformationGroupBy) Aggregate(fns ...AggregateFunc) *VulnInformationGroupBy {
	vigb.fns = append(vigb.fns, fns...)
	return vigb
}

// Scan applies the selector query and scans the result into the given value.
func (vigb *VulnInformationGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, vigb.build.ctx, "GroupBy")
	if err := vigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*VulnInformationQuery, *VulnInformationGroupBy](ctx, vigb.build, vigb, vigb.build.inters, v)
}

func (vigb *VulnInformationGroupBy) sqlScan(ctx context.Context, root *VulnInformationQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(vigb.fns))
	for _, fn := range vigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*vigb.flds)+len(vigb.fns))
		for _, f := range *vigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*vigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := vigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// VulnInformationSelect is the builder for selecting fields of VulnInformation entities.
type VulnInformationSelect struct {
	*VulnInformationQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (vis *VulnInformationSelect) Aggregate(fns ...AggregateFunc) *VulnInformationSelect {
	vis.fns = append(vis.fns, fns...)
	return vis
}

// Scan applies the selector query and scans the result into the given value.
func (vis *VulnInformationSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, vis.ctx, "Select")
	if err := vis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*VulnInformationQuery, *VulnInformationSelect](ctx, vis.VulnInformationQuery, vis, vis.inters, v)
}

func (vis *VulnInformationSelect) sqlScan(ctx context.Context, root *VulnInformationQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(vis.fns))
	for _, fn := range vis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*vis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := vis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
