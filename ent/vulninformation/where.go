// Code generated by ent, DO NOT EDIT.

package vulninformation

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/zema1/watchvuln/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldID, id))
}

// Key applies equality check predicate on the "key" field. It's identical to KeyEQ.
func Key(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldKey, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldTitle, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldDescription, v))
}

// Severity applies equality check predicate on the "severity" field. It's identical to SeverityEQ.
func Severity(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldSeverity, v))
}

// Cve applies equality check predicate on the "cve" field. It's identical to CveEQ.
func Cve(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldCve, v))
}

// Disclosure applies equality check predicate on the "disclosure" field. It's identical to DisclosureEQ.
func Disclosure(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldDisclosure, v))
}

// Solutions applies equality check predicate on the "solutions" field. It's identical to SolutionsEQ.
func Solutions(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldSolutions, v))
}

// From applies equality check predicate on the "from" field. It's identical to FromEQ.
func From(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldFrom, v))
}

// Pushed applies equality check predicate on the "pushed" field. It's identical to PushedEQ.
func Pushed(v bool) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldPushed, v))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldUpdateTime, v))
}

// KeyEQ applies the EQ predicate on the "key" field.
func KeyEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldKey, v))
}

// KeyNEQ applies the NEQ predicate on the "key" field.
func KeyNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldKey, v))
}

// KeyIn applies the In predicate on the "key" field.
func KeyIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldKey, vs...))
}

// KeyNotIn applies the NotIn predicate on the "key" field.
func KeyNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldKey, vs...))
}

// KeyGT applies the GT predicate on the "key" field.
func KeyGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldKey, v))
}

// KeyGTE applies the GTE predicate on the "key" field.
func KeyGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldKey, v))
}

// KeyLT applies the LT predicate on the "key" field.
func KeyLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldKey, v))
}

// KeyLTE applies the LTE predicate on the "key" field.
func KeyLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldKey, v))
}

// KeyContains applies the Contains predicate on the "key" field.
func KeyContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldKey, v))
}

// KeyHasPrefix applies the HasPrefix predicate on the "key" field.
func KeyHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldKey, v))
}

// KeyHasSuffix applies the HasSuffix predicate on the "key" field.
func KeyHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldKey, v))
}

// KeyEqualFold applies the EqualFold predicate on the "key" field.
func KeyEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldKey, v))
}

// KeyContainsFold applies the ContainsFold predicate on the "key" field.
func KeyContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldKey, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldTitle, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldDescription, v))
}

// SeverityEQ applies the EQ predicate on the "severity" field.
func SeverityEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldSeverity, v))
}

// SeverityNEQ applies the NEQ predicate on the "severity" field.
func SeverityNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldSeverity, v))
}

// SeverityIn applies the In predicate on the "severity" field.
func SeverityIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldSeverity, vs...))
}

// SeverityNotIn applies the NotIn predicate on the "severity" field.
func SeverityNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldSeverity, vs...))
}

// SeverityGT applies the GT predicate on the "severity" field.
func SeverityGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldSeverity, v))
}

// SeverityGTE applies the GTE predicate on the "severity" field.
func SeverityGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldSeverity, v))
}

// SeverityLT applies the LT predicate on the "severity" field.
func SeverityLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldSeverity, v))
}

// SeverityLTE applies the LTE predicate on the "severity" field.
func SeverityLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldSeverity, v))
}

// SeverityContains applies the Contains predicate on the "severity" field.
func SeverityContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldSeverity, v))
}

// SeverityHasPrefix applies the HasPrefix predicate on the "severity" field.
func SeverityHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldSeverity, v))
}

// SeverityHasSuffix applies the HasSuffix predicate on the "severity" field.
func SeverityHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldSeverity, v))
}

// SeverityEqualFold applies the EqualFold predicate on the "severity" field.
func SeverityEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldSeverity, v))
}

// SeverityContainsFold applies the ContainsFold predicate on the "severity" field.
func SeverityContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldSeverity, v))
}

// CveEQ applies the EQ predicate on the "cve" field.
func CveEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldCve, v))
}

// CveNEQ applies the NEQ predicate on the "cve" field.
func CveNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldCve, v))
}

// CveIn applies the In predicate on the "cve" field.
func CveIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldCve, vs...))
}

// CveNotIn applies the NotIn predicate on the "cve" field.
func CveNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldCve, vs...))
}

// CveGT applies the GT predicate on the "cve" field.
func CveGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldCve, v))
}

// CveGTE applies the GTE predicate on the "cve" field.
func CveGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldCve, v))
}

// CveLT applies the LT predicate on the "cve" field.
func CveLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldCve, v))
}

// CveLTE applies the LTE predicate on the "cve" field.
func CveLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldCve, v))
}

// CveContains applies the Contains predicate on the "cve" field.
func CveContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldCve, v))
}

// CveHasPrefix applies the HasPrefix predicate on the "cve" field.
func CveHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldCve, v))
}

// CveHasSuffix applies the HasSuffix predicate on the "cve" field.
func CveHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldCve, v))
}

// CveEqualFold applies the EqualFold predicate on the "cve" field.
func CveEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldCve, v))
}

// CveContainsFold applies the ContainsFold predicate on the "cve" field.
func CveContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldCve, v))
}

// DisclosureEQ applies the EQ predicate on the "disclosure" field.
func DisclosureEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldDisclosure, v))
}

// DisclosureNEQ applies the NEQ predicate on the "disclosure" field.
func DisclosureNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldDisclosure, v))
}

// DisclosureIn applies the In predicate on the "disclosure" field.
func DisclosureIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldDisclosure, vs...))
}

// DisclosureNotIn applies the NotIn predicate on the "disclosure" field.
func DisclosureNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldDisclosure, vs...))
}

// DisclosureGT applies the GT predicate on the "disclosure" field.
func DisclosureGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldDisclosure, v))
}

// DisclosureGTE applies the GTE predicate on the "disclosure" field.
func DisclosureGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldDisclosure, v))
}

// DisclosureLT applies the LT predicate on the "disclosure" field.
func DisclosureLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldDisclosure, v))
}

// DisclosureLTE applies the LTE predicate on the "disclosure" field.
func DisclosureLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldDisclosure, v))
}

// DisclosureContains applies the Contains predicate on the "disclosure" field.
func DisclosureContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldDisclosure, v))
}

// DisclosureHasPrefix applies the HasPrefix predicate on the "disclosure" field.
func DisclosureHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldDisclosure, v))
}

// DisclosureHasSuffix applies the HasSuffix predicate on the "disclosure" field.
func DisclosureHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldDisclosure, v))
}

// DisclosureEqualFold applies the EqualFold predicate on the "disclosure" field.
func DisclosureEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldDisclosure, v))
}

// DisclosureContainsFold applies the ContainsFold predicate on the "disclosure" field.
func DisclosureContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldDisclosure, v))
}

// SolutionsEQ applies the EQ predicate on the "solutions" field.
func SolutionsEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldSolutions, v))
}

// SolutionsNEQ applies the NEQ predicate on the "solutions" field.
func SolutionsNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldSolutions, v))
}

// SolutionsIn applies the In predicate on the "solutions" field.
func SolutionsIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldSolutions, vs...))
}

// SolutionsNotIn applies the NotIn predicate on the "solutions" field.
func SolutionsNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldSolutions, vs...))
}

// SolutionsGT applies the GT predicate on the "solutions" field.
func SolutionsGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldSolutions, v))
}

// SolutionsGTE applies the GTE predicate on the "solutions" field.
func SolutionsGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldSolutions, v))
}

// SolutionsLT applies the LT predicate on the "solutions" field.
func SolutionsLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldSolutions, v))
}

// SolutionsLTE applies the LTE predicate on the "solutions" field.
func SolutionsLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldSolutions, v))
}

// SolutionsContains applies the Contains predicate on the "solutions" field.
func SolutionsContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldSolutions, v))
}

// SolutionsHasPrefix applies the HasPrefix predicate on the "solutions" field.
func SolutionsHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldSolutions, v))
}

// SolutionsHasSuffix applies the HasSuffix predicate on the "solutions" field.
func SolutionsHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldSolutions, v))
}

// SolutionsEqualFold applies the EqualFold predicate on the "solutions" field.
func SolutionsEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldSolutions, v))
}

// SolutionsContainsFold applies the ContainsFold predicate on the "solutions" field.
func SolutionsContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldSolutions, v))
}

// ReferencesIsNil applies the IsNil predicate on the "references" field.
func ReferencesIsNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIsNull(FieldReferences))
}

// ReferencesNotNil applies the NotNil predicate on the "references" field.
func ReferencesNotNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotNull(FieldReferences))
}

// TagsIsNil applies the IsNil predicate on the "tags" field.
func TagsIsNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIsNull(FieldTags))
}

// TagsNotNil applies the NotNil predicate on the "tags" field.
func TagsNotNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotNull(FieldTags))
}

// GithubSearchIsNil applies the IsNil predicate on the "github_search" field.
func GithubSearchIsNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIsNull(FieldGithubSearch))
}

// GithubSearchNotNil applies the NotNil predicate on the "github_search" field.
func GithubSearchNotNil() predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotNull(FieldGithubSearch))
}

// FromEQ applies the EQ predicate on the "from" field.
func FromEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldFrom, v))
}

// FromNEQ applies the NEQ predicate on the "from" field.
func FromNEQ(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldFrom, v))
}

// FromIn applies the In predicate on the "from" field.
func FromIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldFrom, vs...))
}

// FromNotIn applies the NotIn predicate on the "from" field.
func FromNotIn(vs ...string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldFrom, vs...))
}

// FromGT applies the GT predicate on the "from" field.
func FromGT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldFrom, v))
}

// FromGTE applies the GTE predicate on the "from" field.
func FromGTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldFrom, v))
}

// FromLT applies the LT predicate on the "from" field.
func FromLT(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldFrom, v))
}

// FromLTE applies the LTE predicate on the "from" field.
func FromLTE(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldFrom, v))
}

// FromContains applies the Contains predicate on the "from" field.
func FromContains(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContains(FieldFrom, v))
}

// FromHasPrefix applies the HasPrefix predicate on the "from" field.
func FromHasPrefix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasPrefix(FieldFrom, v))
}

// FromHasSuffix applies the HasSuffix predicate on the "from" field.
func FromHasSuffix(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldHasSuffix(FieldFrom, v))
}

// FromEqualFold applies the EqualFold predicate on the "from" field.
func FromEqualFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEqualFold(FieldFrom, v))
}

// FromContainsFold applies the ContainsFold predicate on the "from" field.
func FromContainsFold(v string) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldContainsFold(FieldFrom, v))
}

// PushedEQ applies the EQ predicate on the "pushed" field.
func PushedEQ(v bool) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldPushed, v))
}

// PushedNEQ applies the NEQ predicate on the "pushed" field.
func PushedNEQ(v bool) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldPushed, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldCreateTime, v))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.VulnInformation {
	return predicate.VulnInformation(sql.FieldLTE(FieldUpdateTime, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.VulnInformation) predicate.VulnInformation {
	return predicate.VulnInformation(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.VulnInformation) predicate.VulnInformation {
	return predicate.VulnInformation(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.VulnInformation) predicate.VulnInformation {
	return predicate.VulnInformation(sql.NotPredicates(p))
}
