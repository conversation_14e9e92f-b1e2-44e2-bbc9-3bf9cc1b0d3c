#!/bin/bash

# 生成protobuf代码的脚本

# 检查protoc是否安装
if ! command -v protoc &> /dev/null; then
    echo "protoc 未安装，请先安装 Protocol Buffers compiler"
    echo "参考: https://grpc.io/docs/protoc-installation/"
    exit 1
fi

# 检查protoc-gen-go是否安装
if ! command -v protoc-gen-go &> /dev/null; then
    echo "protoc-gen-go 未安装，正在安装..."
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
fi

# 检查protoc-gen-go-grpc是否安装
if ! command -v protoc-gen-go-grpc &> /dev/null; then
    echo "protoc-gen-go-grpc 未安装，正在安装..."
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
fi

# 创建输出目录
mkdir -p push

# 生成Go代码
echo "正在生成protobuf代码..."
protoc --go_out=. --go_opt=paths=source_relative \
       --go-grpc_out=. --go-grpc_opt=paths=source_relative \
       proto/vuln_pusher.proto

echo "protobuf代码生成完成！"
echo "生成的文件:"
echo "  - push/vuln_pusher.pb.go"
echo "  - push/vuln_pusher_grpc.pb.go"
