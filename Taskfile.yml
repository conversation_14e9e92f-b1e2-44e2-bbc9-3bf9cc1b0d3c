version: "3"

dotenv:
  - .env

env:
  CGO_ENABLED: 0

vars:
  BINARY_NAME: watchvuln
  # 已经在跳板机上配置好扫描节点的认证信息
  RUNNER_SERVER: watchvuln-runner

tasks:
  build:
    cmds:
      - GOOS=linux GOARCH=amd64 go build -trimpath -o ./output/{{.BINARY_NAME}} .
  update:
    deps:
      - build
    cmds:
      # 先将容器停止
      - ssh {{.RUNNER_SERVER}} "cd /opt/watchvuln && docker compose stop"
      - scp ./output/{{.BINARY_NAME}} {{.RUNNER_SERVER}}:/opt/watchvuln/
      - ssh {{.RUNNER_SERVER}} "cd /opt/watchvuln && docker compose start"
  deploy:
    deps:
      - build
    cmds:
      - ssh {{.RUNNER_SERVER}} "mkdir /opt/watchvuln"
      - scp ./output/{{.BINARY_NAME}} ./Dockerfile ./docker-compose.yaml {{.RUNNER_SERVER}}:/opt/watchvuln/
      - echo "部署完成，需要自行到运行机器上创建一个.env文件并配置token和密钥，然后运行 docker compose up -d"
