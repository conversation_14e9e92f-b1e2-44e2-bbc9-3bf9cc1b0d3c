version: '3.8'

services:
  db:
    image: postgres:14.4-alpine
    restart: 'always'
    environment:
      POSTGRES_DB: poc_finder_db
      POSTGRES_USER: poc_finder
      POSTGRES_PASSWORD: poc_finder
    volumes:
      - "./initdb:/docker-entrypoint-initdb.d"

  poc_finder:
    build:
      context: .
      dockerfile: Dockerfile
    image: poc_finder:latest
    restart: 'always'
    working_dir: /app
    entrypoint: ["poc_finder"]
    command: ["daemon"]
    volumes:
      - ./vulnlist.txt:/app/vulnlist.txt
      - ./config.toml:/app/config.toml
    depends_on:
      - db