package main

import (
	"flag"
	"fmt"
	"log"
	"time"

	"github.com/zema1/watchvuln/grab"
	"github.com/zema1/watchvuln/push"
)

func main() {
	// 命令行参数
	var (
		address = flag.String("addr", "localhost:50051", "gRPC服务器地址")
		timeout = flag.Int("timeout", 300, "超时时间（秒）")
		count   = flag.Int("count", 1, "推送漏洞数量")
	)
	flag.Parse()

	fmt.Printf("🚀 开始向 %s 推送漏洞信息...\n", *address)
	fmt.Printf("⏰ 超时时间: %d 秒\n", *timeout)
	fmt.Printf("📊 推送数量: %d 个漏洞\n", *count)
	fmt.Println()

	// 创建gRPC推送器
	config := &push.GrpcConfig{
		Address:  *address,
		Timeout:  *timeout,
		Insecure: true,
	}

	rawPusher, err := push.NewGrpcPusher(config)
	if err != nil {
		log.Fatalf("❌ 创建gRPC推送器失败: %v", err)
	}
	grpcPusher := rawPusher.(*push.GrpcPusher)
	defer grpcPusher.Close()

	fmt.Printf("✅ 成功连接到 %s\n\n", *address)

	// 1. 推送初始化消息
	fmt.Println("📡 推送初始化消息...")
	err = pushInitialMessage(rawPusher)
	if err != nil {
		log.Printf("❌ 初始化消息推送失败: %v", err)
	} else {
		fmt.Println("✅ 初始化消息推送成功")
	}
	fmt.Println()

	// 2. 推送漏洞信息
	fmt.Printf("🔍 开始推送 %d 个漏洞信息...\n", *count)
	successCount := 0

	vulns := generateTestVulns(*count)
	for i, vuln := range vulns {
		fmt.Printf("📤 推送漏洞 %d/%d: %s\n", i+1, len(vulns), vuln.Title)

		rawMsg := push.NewRawVulnInfoMessage(vuln)
		err = rawPusher.PushRaw(rawMsg)
		if err != nil {
			log.Printf("❌ 漏洞推送失败: %v", err)
		} else {
			successCount++
			fmt.Printf("✅ 成功 (CVE: %s, 严重程度: %s)\n", vuln.CVE, vuln.Severity)
		}

		// 添加小延迟避免过快推送
		time.Sleep(100 * time.Millisecond)
	}
	fmt.Println()

	// 3. 推送状态消息
	fmt.Println("📝 推送状态消息...")
	statusMessages := []string{
		"手动测试开始",
		fmt.Sprintf("推送了 %d 个漏洞", successCount),
		"POC搜索引擎已就绪",
		"手动测试完成",
	}

	for _, message := range statusMessages {
		rawMsg := push.NewRawTextMessage(message)
		err := rawPusher.PushRaw(rawMsg)
		if err != nil {
			log.Printf("❌ 状态消息推送失败: %v", err)
		} else {
			fmt.Printf("   ✅ %s\n", message)
		}
	}
	fmt.Println()

	// 4. 输出总结
	fmt.Println("📊 推送总结:")
	fmt.Printf("   - 总漏洞数: %d\n", len(vulns))
	fmt.Printf("   - 成功推送: %d\n", successCount)
	fmt.Printf("   - 失败推送: %d\n", len(vulns)-successCount)
	fmt.Printf("   - 成功率: %.1f%%\n", float64(successCount)/float64(len(vulns))*100)
	fmt.Println()

	if successCount == len(vulns) {
		fmt.Println("🎉 所有漏洞推送成功！")
	} else {
		fmt.Println("⚠️  部分漏洞推送失败，请检查服务器状态")
	}

	fmt.Println("\n💡 提示: 请检查POC-Finder服务器日志确认数据接收情况")
}

// pushInitialMessage 推送初始化消息
func pushInitialMessage(pusher push.RawPusher) error {
	initialMsg := &push.InitialMessage{
		Version:   "v1.0.0-manual-test",
		VulnCount: 200,
		Interval:  "30m",
		Provider: []*grab.Provider{
			{
				Name:        "avd",
				DisplayName: "阿里云漏洞库",
				Link:        "https://avd.aliyun.com",
			},
			{
				Name:        "ti",
				DisplayName: "奇安信威胁情报中心",
				Link:        "https://ti.qianxin.com",
			},
			{
				Name:        "oscs",
				DisplayName: "OSCS开源安全情报预警",
				Link:        "https://www.oscs1024.com",
			},
		},
		FailedProvider: []*grab.Provider{
			{
				Name:        "seebug",
				DisplayName: "知道创宇Seebug漏洞库",
				Link:        "https://www.seebug.org",
			},
		},
	}

	rawMsg := push.NewRawInitialMessage(initialMsg)
	return pusher.PushRaw(rawMsg)
}

// generateTestVulns 生成测试漏洞数据
func generateTestVulns(count int) []*grab.VulnInfo {
	vulns := []*grab.VulnInfo{
		{
			Title:       "WordPress Stacks Mobile App Builder <=5.2.3 - Authentication Bypass",
			CVE:         "CVE-2024-50477",
			Description: "Stacks Mobile App Builder WordPress plugin ≤ 5.2.3 suffers from an authentication bypass vulnerability via improper handling of query parameters, allowing attackers to impersonate arbitrary users.",
			Severity:    grab.Critical,
			Tags:        []string{"nuclei-templates 更新poc"},
			From:        "https://ti.qianxin.com/vulnerability/detail/",
			References: []string{"https://github.com/stealthcopter/wordpress-hacking/blob/main/reports/stacks-mobile-app-builder-priv-esc/stacks-mobile-app-builder-priv-esc.md",
				"https://github.com/RandomRobbieBF/CVE-2024-50477"},
			Reason:     []string{"漏洞创建"},
			Disclosure: "2025-07-14",
		},
		{
			Title:       "XWiki getdeleteddocuments.vm SQL注入漏洞",
			CVE:         "CVE-2025-32429",
			Description: "SQL注入漏洞是指攻击者通过在Web应用程序的输入字段中插入恶意SQL代码，从而绕过应用程序的安全措施，直接对数据库执行非法操作。这种漏洞通常发生在应用程序未对用户输入进行充分验证和过滤的情况下，使得攻击者能够获取、修改或删除数据库中的数据，甚至可能执行服务器上的任意代码。",
			Severity:    grab.High,
			Tags:        []string{""},
			From:        "https://stack.chaitin.com/vuldb/detail/540e58f2-e715-4159-a237-667599bb74b8",
			Reason:      []string{"漏洞创建"},
			Disclosure:  "2025-07-25",
		},
		{
			Title:       "WordPress Plugin XSS漏洞",
			Description: "WordPress某插件存在存储型XSS漏洞，已有nuclei检测模板。",
			Severity:    grab.Medium,
			Tags:        []string{"XSS", "WordPress", "nuclei-templates"},
			From:        "https://www.oscs1024.com/hd/",
			Reason:      []string{"nuclei模板可用", "WordPress漏洞"},
		},
		{
			Title:       "TP-Link TL-WR840N - Command Injection",
			CVE:         "CVE-2022-25061",
			Description: "The TP-Link TL-WR840N(ES)_V6.20_180709 router contains a command injection vulnerability in the oal_setIp6DefaultRoute component. This vulnerability allows authenticated attackers to execute arbitrary system commands, leading to complete device compromise.",
			Severity:    grab.Critical,
			Tags:        []string{"nuclei-templates 更新poc"},
			From:        "https://api.projectdiscovery.io/v1/template/early",
			Reason:      []string{"漏洞创建"},
			Disclosure:  "2025-05-19",
		},
		{
			Title:       "FreeType Out-of-Bounds Write Vulnerability",
			CVE:         "CVE-2025-27363",
			Description: "FreeType contains an out-of-bounds write vulnerability when attempting to parse font subglyph structures related to TrueType GX and variable font files that may allow for arbitrary code execution.",
			Severity:    grab.Critical,
			Tags:        []string{"FreeType FreeType 在野利用"},
			From:        "https://www.cisa.gov/known-exploited-vulnerabilities-catalog",
			Reason:      []string{"漏洞创建"},
			Disclosure:  "2025-05-06",
		},
	}

	//vulns := make([]*grab.VulnInfo, count)
	//for i := 0; i < count; i++ {
	//	template := templates[i%len(templates)]
	//	vuln := &grab.VulnInfo{
	//		UniqueKey:    fmt.Sprintf("manual-test-%03d", i+1),
	//		Title:        fmt.Sprintf("%s #%d", template.Title, i+1),
	//		Description:  template.Description,
	//		Severity:     template.Severity,
	//		CVE:          fmt.Sprintf("CVE-2024-MANUAL%03d", i+1),
	//		Disclosure:   time.Now().Format("2006-01-02"),
	//		Solutions:    "请参考官方安全公告进行修复",
	//		GithubSearch: []string{fmt.Sprintf("https://github.com/search?q=CVE-2024-MANUAL%03d", i+1)},
	//		References:   []string{fmt.Sprintf("https://nvd.nist.gov/vuln/detail/CVE-2024-MANUAL%03d", i+1)},
	//		Tags:         template.Tags,
	//		From:         fmt.Sprintf("%sMANUAL%03d", template.From, i+1),
	//		Reason:       template.Reason,
	//	}
	//	vulns[i] = vuln
	//}

	return vulns
}
