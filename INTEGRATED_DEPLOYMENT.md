# WatchVuln + POC-Finder 集成部署指南

本文档详细说明如何将 WatchVuln 和 POC-Finder 两个项目集成部署，实现漏洞监控到 POC 查找的完整工作流。

## 架构概述

```
┌─────────────┐    gRPC     ┌─────────────┐
│  WatchVuln  │ ──────────> │ POC-Finder  │
│             │             │             │
│ 漏洞监控推送  │             │ POC查找分析   │
└─────────────┘             └─────────────┘
       │                           │
       ▼                           ▼
┌─────────────┐             ┌─────────────┐
│ WatchVuln   │             │ POC-Finder  │
│ PostgreSQL  │             │ PostgreSQL  │
└─────────────┘             └─────────────┘
```

## 快速开始

### 1. 准备工作

确保您的系统已安装：
- Docker
- Docker Compose
- Git

### 2. 检查项目结构

确保您的目录结构如下：
```
watchvuln/
├── docker-compose-integrated.yaml    # 完整集成配置
├── docker-compose-simple.yaml        # 简化集成配置
├── .env.example                      # 环境变量示例
├── test/poc-finder/                  # POC-Finder 项目
│   ├── Dockerfile
│   ├── config.toml
│   ├── vulnlist.txt
│   └── initdb/                       # 数据库初始化脚本
└── ... (其他 WatchVuln 文件)
```

### 3. 配置环境变量

```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

关键配置项：
```bash
# gRPC 配置（WatchVuln -> POC-Finder）
GRPC_ADDRESS=poc-finder:50051
GRPC_TIMEOUT=300
GRPC_INSECURE=true

# 可选：钉钉推送（备用通知）
DINGDING_ACCESS_TOKEN=your_token_here
DINGDING_SECRET=your_secret_here

# POC-Finder 配置
POC_FINDER_GRPC_PORT=50051
POC_FINDER_LOG_LEVEL=info
```

### 4. 启动服务

#### 选项 A：简化部署（推荐新手）
```bash
docker-compose -f docker-compose-simple.yaml up -d
```

#### 选项 B：完整部署（推荐生产）
```bash
docker-compose -f docker-compose-integrated.yaml up -d
```

#### 选项 C：带管理工具
```bash
# 启动包含数据库管理工具
docker-compose -f docker-compose-simple.yaml --profile tools up -d
```

### 5. 验证部署

```bash
# 检查服务状态
docker-compose -f docker-compose-simple.yaml ps

# 查看日志
docker-compose -f docker-compose-simple.yaml logs -f watchvuln
docker-compose -f docker-compose-simple.yaml logs -f poc-finder

# 检查 gRPC 连接
docker exec watchvuln curl -f http://poc-finder:50051 || echo "gRPC service is running"
```

## 配置选项详解

### WatchVuln 配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `GRPC_ADDRESS` | `poc-finder:50051` | POC-Finder gRPC 服务地址 |
| `GRPC_TIMEOUT` | `300` | gRPC 连接超时时间（秒） |
| `GRPC_INSECURE` | `true` | 是否使用不安全连接 |
| `INTERVAL` | `30m` | 漏洞检查间隔 |
| `ENABLE_CVE_FILTER` | `true` | 启用 CVE 去重过滤 |

### POC-Finder 配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `POC_FINDER_GRPC_PORT` | `50051` | gRPC 服务监听端口 |
| `POC_FINDER_GRPC_HOST` | `0.0.0.0` | gRPC 服务监听地址 |
| `POC_FINDER_LOG_LEVEL` | `info` | 日志级别 |
| `POC_FINDER_WORKER_COUNT` | `4` | 工作线程数 |

## 网络配置

### Docker 内部网络

两个服务通过 Docker 内部网络 `intelli-sec` 进行通信：

- WatchVuln 作为 gRPC 客户端
- POC-Finder 作为 gRPC 服务器
- 数据库服务独立运行

### 端口映射

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|---------|---------|------|
| WatchVuln DB | 5432 | 5432 | PostgreSQL |
| POC-Finder DB | 5432 | 5433 | PostgreSQL |
| POC-Finder gRPC | 50051 | - | 内部通信 |
| Adminer | 8080 | 8080 | 数据库管理（可选） |

## 数据持久化

### 数据卷配置

```yaml
volumes:
  - "./data/watchvuln-db:/var/lib/postgresql/data"     # WatchVuln 数据库
  - "./data/poc-finder-db:/var/lib/postgresql/data"    # POC-Finder 数据库
  - "./logs/watchvuln:/app/logs"                       # WatchVuln 日志
  - "./logs/poc-finder:/app/logs"                      # POC-Finder 日志
```

### 备份策略

```bash
# 备份 WatchVuln 数据库
docker exec watchvuln-db pg_dump -U watchvuln watchvuln > backup-watchvuln.sql

# 备份 POC-Finder 数据库
docker exec poc-finder-db pg_dump -U poc_finder poc_finder_db > backup-poc-finder.sql

# 恢复数据库
docker exec -i watchvuln-db psql -U watchvuln watchvuln < backup-watchvuln.sql
```

## 监控和维护

### 健康检查

所有服务都配置了健康检查：

```bash
# 检查服务健康状态
docker-compose -f docker-compose-simple.yaml ps

# 查看详细健康状态
docker inspect --format='{{.State.Health.Status}}' watchvuln
docker inspect --format='{{.State.Health.Status}}' poc-finder
```

### 日志管理

```bash
# 实时查看日志
docker-compose -f docker-compose-simple.yaml logs -f

# 查看特定服务日志
docker-compose -f docker-compose-simple.yaml logs -f watchvuln
docker-compose -f docker-compose-simple.yaml logs -f poc-finder

# 日志轮转已配置，最大 10MB，保留 3 个文件
```

### 性能调优

1. **数据库优化**：
   ```bash
   # 调整 PostgreSQL 配置
   # 在 docker-compose 中添加：
   command: postgres -c shared_preload_libraries=pg_stat_statements
   ```

2. **内存限制**：
   ```yaml
   deploy:
     resources:
       limits:
         memory: 512M
       reservations:
         memory: 256M
   ```

## 故障排除

### 常见问题

1. **gRPC 连接失败**
   ```bash
   # 检查网络连接
   docker exec watchvuln ping poc-finder
   
   # 检查端口
   docker exec poc-finder netstat -tlnp | grep 50051
   ```

2. **数据库连接问题**
   ```bash
   # 检查数据库状态
   docker exec watchvuln-db pg_isready -U watchvuln
   docker exec poc-finder-db pg_isready -U poc_finder
   ```

3. **服务启动顺序问题**
   ```bash
   # 重新启动服务（按依赖顺序）
   docker-compose -f docker-compose-simple.yaml down
   docker-compose -f docker-compose-simple.yaml up -d
   ```

### 调试模式

```bash
# 启用详细日志
export POC_FINDER_LOG_LEVEL=debug
export LOG_LEVEL=debug

# 重新启动服务
docker-compose -f docker-compose-simple.yaml up -d
```

## 升级和维护

### 更新镜像

```bash
# 停止服务
docker-compose -f docker-compose-simple.yaml down

# 重新构建镜像
docker-compose -f docker-compose-simple.yaml build --no-cache

# 启动服务
docker-compose -f docker-compose-simple.yaml up -d
```

### 清理资源

```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的卷
docker volume prune -f

# 完全清理（谨慎使用）
docker system prune -a --volumes
```
