db_conn: sqlite3://integration_test.sqlite3
interval: 30m
enable_cve_filter: true
no_github_search: false
no_start_message: true
diff_mode: false
white_keywords: [ ]
black_keywords: [ ]
skip_tls_verify: false
proxy: ""
test: true

# 测试用的数据源，只使用轻量级的源
sources: ["avd"]

pusher:
  # 钉钉推送配置 - 集成测试
  - type: dingding
    access_token: "9e9988a0950e87d82d721b028bd591b46f1c523a9f8dcb4e0867c0f5fd99ac82"
    sign_secret: "SECdce4038b82e06dcb1bc532c678b2f0fb13a1a26680bcff40b8408195dcb122cc"

  # GRPC推送配置 - 集成测试
  - type: grpc
    address: "localhost:50051"
