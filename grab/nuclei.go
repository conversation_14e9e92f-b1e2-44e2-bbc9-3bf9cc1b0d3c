package grab

import (
	"cmp"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"

	"github.com/zema1/watchvuln/util"

	"github.com/imroc/req/v3"
	"github.com/kataras/golog"
)

var urlRegex = regexp.MustCompile(`https?://[^\s]+`)

// ProjectDiscoveryCrawler 结构体实现漏洞抓取器接口
// 用于从平台获取json漏洞信息
type ProjectDiscoveryCrawler struct {
	client *req.Client   // HTTP客户端，用于发送请求
	log    *golog.Logger // 日志记录器
}

// NewProjectDiscoveryCrawler 创建新的ProjectDiscovery爬虫实例
func NewProjectDiscoveryCrawler() Grabber {
	client := util.WrapApiClient(util.NewHttpClient())
	client.SetCommonHeader("X-API-Key", "547edab6-f6b8-4669-a365-4728c2290d7b")
	fmt.Println("test for nuclei")
	return &ProjectDiscoveryCrawler{
		log:    golog.Child("[project-discovery]"),
		client: client,
	}
}

// ProviderInfo 返回威胁情报源信息
func (p *ProjectDiscoveryCrawler) ProviderInfo() *Provider {
	return &Provider{
		Name:        "project-discovery",
		DisplayName: "ProjectDiscovery漏洞库",
		Link:        "https://projectdiscovery.io/",
	}
}

// GetUpdate 实现漏洞信息获取逻辑
func (p *ProjectDiscoveryCrawler) GetUpdate(ctx context.Context, _ int) ([]*VulnInfo, error) {
	url := "https://api.projectdiscovery.io/v1/template/early"

	resp, err := p.client.R().
		SetContext(ctx).
		Get(url)
	if err != nil {
		return nil, fmt.Errorf("request failed: %v", err)
	}
	defer func() {
		_ = resp.Response.Body.Close()
	}()

	// 读取响应体内容
	body, err := io.ReadAll(resp.Response.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %v", err)
	}

	var data Document
	if err = json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("json decode failed: %v", err)
	}

	var results []*VulnInfo

	for _, result := range data.Results {
		var cveID string
		if len(result.Classification.CVEID) != 0 {
			cveID = strings.ToLower(result.Classification.CVEID[0])
		} else if strings.HasPrefix(result.ID, "CVE-") {
			cveID = result.ID
		}

		refContent := p.extractReferenceContent(result.Raw)
		references := urlRegex.FindAllString(refContent, -1)

		info := &VulnInfo{
			UniqueKey:   cmp.Or(cveID, result.ID), // 有cve-id就用，没有的话就用poc-id做唯一key
			Title:       result.Name,
			Description: result.Description,
			Severity:    parseSeverity(result.Severity),
			CVE:         cveID,
			Disclosure:  result.CreatedAt.Format("2006-01-02"),
			References:  references,
			Tags:        []string{"nuclei-templates 更新poc"},
			Solutions:   "",
			From:        "https://api.projectdiscovery.io/v1/template/early",
			Creator:     p,
		}

		results = append(results, info)
	}

	// 去重逻辑
	uniq := make(map[string]bool)
	var filtered []*VulnInfo
	for _, info := range results {
		if !uniq[info.UniqueKey] {
			filtered = append(filtered, info)
			uniq[info.UniqueKey] = true
		}
	}

	p.log.Infof("got %d vulns from projectdiscovery api", len(filtered))
	return filtered, nil
}

// IsValuable 判断漏洞是否有价值
func (p *ProjectDiscoveryCrawler) IsValuable(info *VulnInfo) bool {
	return info.Severity == High || info.Severity == Critical
}

// extractReferenceContent 提取参考内容
func (p *ProjectDiscoveryCrawler) extractReferenceContent(raw string) string {
	start := strings.Index(raw, "reference")
	end := strings.Index(raw, "classification")
	if start == -1 || end == -1 || end < start {
		return ""
	}
	return raw[start+len("reference") : end]
}

// parseSeverity 转换严重性等级
func parseSeverity(s string) SeverityLevel {
	switch strings.ToLower(s) {
	case "critical":
		return Critical
	case "high":
		return High
	case "medium":
		return Medium
	case "low":
		return Low
	default:
		return Low
	}
}

// Document 响应数据结构
type Document struct {
	Results []struct {
		ID             string `json:"id"`
		Classification struct {
			CVEID []string `json:"cve-id"`
		} `json:"classification"`
		Description string    `json:"description"`
		Name        string    `json:"name"`
		Severity    string    `json:"severity"`
		Raw         string    `json:"raw"`
		CreatedAt   time.Time `json:"created_at"`
		UpdatedAt   time.Time `json:"updated_at"`
	} `json:"results"`
}
