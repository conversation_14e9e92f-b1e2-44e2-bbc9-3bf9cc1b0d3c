# This file is licensed under the terms of the MIT license https://opensource.org/license/mit
# Copyright (c) 2021-2025 <PERSON><PERSON>

## Golden config for golangci-lint v1.64.5
#
# This is the best config for golangci-lint based on my experience and opinion.
# It is very strict, but not extremely strict.
# Feel free to adapt it to suit your needs.
# If this config helps you, please consider keeping a link to this file (see the next comment).

# Based on https://gist.github.com/maratori/47a4d00457a92aa426dbd48a18776322

run:
  # Timeout for analysis, e.g. 30s, 5m.
  # Default: 1m
  timeout: 3m
  tests: false  # 这是关键设置，禁用对测试文件的检查
  # The mode used to evaluate relative paths.
  # It's used by exclusions, Go plugins, and some linters.
  # The value can be:
  # - `gomod`: the paths will be relative to the directory of the `go.mod` file.
  # - `gitroot`: the paths will be relative to the git root (the parent directory of `.git`).
  # - `cfg`: the paths will be relative to the configuration file.
  # - `wd` (NOT recommended): the paths will be relative to the place where golangci-lint is run.
  # Default: wd
  relative-path-mode: gomod

output:
  sort-results: true

# This file contains only configs which differ from defaults.
# All possible options can be found here https://github.com/golangci/golangci-lint/blob/HEAD/.golangci.reference.yml
linters-settings:
  cyclop:
    # The maximal code complexity to report.
    # Default: 10
    max-complexity: 30
    # The maximal average package complexity.
    # If it's higher than 0.0 (float) the check is enabled
    # Default: 0.0
    package-average: 10.0

  errcheck:
    # Report about not checking of errors in type assertions: `a := b.(MyStruct)`.
    # Such cases aren't reported by default.
    # Default: false
    check-type-assertions: true

  exhaustive:
    # Program elements to check for exhaustiveness.
    # Default: [ switch ]
    check:
      - switch
      - map

  exhaustruct:
    # List of regular expressions to exclude struct packages and their names from checks.
    # Regular expressions must match complete canonical struct package/name/structname.
    # Default: []
    exclude:
      # std libs
      - "^net/http.Client$"
      - "^net/http.Cookie$"
      - "^net/http.Request$"
      - "^net/http.Response$"
      - "^net/http.Server$"
      - "^net/http.Transport$"
      - "^net/url.URL$"
      - "^os/exec.Cmd$"
      - "^reflect.StructField$"
      # public libs
      - "^github.com/Shopify/sarama.Config$"
      - "^github.com/Shopify/sarama.ProducerMessage$"
      - "^github.com/mitchellh/mapstructure.DecoderConfig$"
      - "^github.com/prometheus/client_golang/.+Opts$"
      - "^github.com/spf13/cobra.Command$"
      - "^github.com/spf13/cobra.CompletionOptions$"
      - "^github.com/stretchr/testify/mock.Mock$"
      - "^github.com/testcontainers/testcontainers-go.+Request$"
      - "^github.com/testcontainers/testcontainers-go.FromDockerfile$"
      - "^golang.org/x/tools/go/analysis.Analyzer$"
      - "^google.golang.org/protobuf/.+Options$"
      - "^gopkg.in/yaml.v3.Node$"

  funlen:
    # Checks the number of lines in a function.
    # If lower than 0, disable the check.
    # Default: 60
    lines: 100
    # Checks the number of statements in a function.
    # If lower than 0, disable the check.
    # Default: 40
    statements: 50
    # Ignore comments when counting lines.
    # Default false
    ignore-comments: true

  gochecksumtype:
    # Presence of `default` case in switch statements satisfies exhaustiveness, if all members are not listed.
    # Default: true
    default-signifies-exhaustive: false

  gocognit:
    # Minimal code complexity to report.
    # Default: 30 (but we recommend 10-20)
    min-complexity: 20

  gocritic:
    # Settings passed to gocritic.
    # The settings key is the name of a supported gocritic checker.
    # The list of supported checkers can be find in https://go-critic.github.io/overview.
    settings:
      captLocal:
        # Whether to restrict checker to params only.
        # Default: true
        paramsOnly: false
      underef:
        # Whether to skip (*x).method() calls where x is a pointer receiver.
        # Default: true
        skipRecvDeref: false

  goimports:
    # A comma-separated list of prefixes, which, if set, checks import paths
    # with the given prefixes are grouped after 3rd-party packages.
    # Default: ""
    local-prefixes: github.acme.red/pictor/asm

  gomoddirectives:
    replace-local: true

  gomodguard:
    blocked:
      # List of blocked modules.
      # Default: []
      modules:
        - github.com/golang/protobuf:
            recommendations:
              - google.golang.org/protobuf
            reason: "see https://developers.google.com/protocol-buffers/docs/reference/go/faq#modules"
        - github.com/satori/go.uuid:
            recommendations:
              - github.com/google/uuid
            reason: "satori's package is not maintained"
        - github.com/gofrs/uuid:
            recommendations:
              - github.com/gofrs/uuid/v5
            reason: "gofrs' package was not go module before v5"

  gosec:
    excludes:
      - G108

  govet:
    # Enable all analyzers.
    # Default: false
    enable-all: true
    # Disable analyzers by name.
    # Run `go tool vet help` to see all analyzers.
    # Default: []
    disable:
      - fieldalignment # too strict
      - shadow # too many bad case

  inamedparam:
    # Skips check for interface methods with only a single parameter.
    # Default: false
    skip-single-param: true

  mnd:
    # List of function patterns to exclude from analysis.
    # Values always ignored: `time.Date`,
    # `strconv.FormatInt`, `strconv.FormatUint`, `strconv.FormatFloat`,
    # `strconv.ParseInt`, `strconv.ParseUint`, `strconv.ParseFloat`.
    # Default: []
    ignored-functions:
      - args.Error
      - flag.Arg
      - flag.Duration.*
      - flag.Float.*
      - flag.Int.*
      - flag.Uint.*
      - os.Chmod
      - os.Mkdir.*
      - os.OpenFile
      - os.WriteFile
      - prometheus.ExponentialBuckets.*
      - prometheus.LinearBuckets

  nakedret:
    # Make an issue if func has more lines of code than this setting, and it has naked returns.
    # Default: 30
    max-func-lines: 0

  nolintlint:
    # Exclude following linters from requiring an explanation.
    # Default: []
    allow-no-explanation: [ funlen, gocognit, lll ]
    # Enable to require an explanation of nonzero length after each nolint directive.
    # Default: false
    require-explanation: true
    # Enable to require nolint directives to mention the specific linter being suppressed.
    # Default: false
    require-specific: true

  perfsprint:
    # Optimizes into strings concatenation.
    # Default: true
    strconcat: false

  reassign:
    # Patterns for global variable names that are checked for reassignment.
    # See https://github.com/curioswitch/go-reassign#usage
    # Default: ["EOF", "Err.*"]
    patterns:
      - ".*"

  revive:
    rules:
      - name: blank-imports
        disabled: true

  rowserrcheck:
    # database/sql is always checked
    # Default: []
    packages:
      - github.com/jmoiron/sqlx

  sloglint:
    # Enforce not using global loggers.
    # Values:
    # - "": disabled
    # - "all": report all global loggers
    # - "default": report only the default slog logger
    # https://github.com/go-simpler/sloglint?tab=readme-ov-file#no-global
    # Default: ""
    no-global: ""
    # Enforce using methods that accept a context.
    # Values:
    # - "": disabled
    # - "all": report all contextless calls
    # - "scope": report only if a context exists in the scope of the outermost function
    # https://github.com/go-simpler/sloglint?tab=readme-ov-file#context-only
    # Default: ""
    context: "scope"

  usetesting:
    # Enable/disable `os.TempDir()` detections.
    # Default: false
    os-temp-dir: true


linters:
  disable-all: true
  enable:
    - errcheck      # 检查未处理的错误
    - gosimple      # 检查代码可以简化的地方
    - govet         # 检查Go常见错误
    - ineffassign   # 检查无效赋值
    - staticcheck   # Go静态分析
    - typecheck     # Go类型检查
    - unused        # 检查未使用的代码
    - gocyclo       # 检查函数复杂度
    - funlen        # 检查函数长度
    - gofmt         # 检查代码格式
    - goimports     # 检查imports格式
    - misspell      # 检查拼写错误
    - whitespace    # 检查空白
    - stylecheck    # 检查代码风格
    - gosec         # 检查安全问题
    - revive        # 代码检查工具

issues:
  #  new: true
  new-from-rev: "origin/main" # 检查相对于main分支的新代码
  exclude-rules:
    - source: "(noinspection|TODO)"
      linters: [ godot ]
    - source: "//noinspection"
      linters: [ gocritic ]
    - path: ".*_test\\.go"
      linters:
        - all
    - path: "pkg/npoc/httpv/client.go"
      linters:
        - gosec
#    - source: "promauto"
#      linters: [ gochecknoglobals ]
