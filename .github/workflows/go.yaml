# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: Go

on:
  pull_request:
    branches: [ "main" ]

jobs:

  build:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整的 git 历史
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          cache: false

      - run: go env -w GOPRIVATE=github.acme.red

      - name: Build
        run: go build -v ./...


      - name: Golangci-lint
        uses: golangci/golangci-lint-action@v6.5.0