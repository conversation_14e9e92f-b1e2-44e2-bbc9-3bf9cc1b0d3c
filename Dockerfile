#FROM golang:1.23-alpine as builder
#
#
#WORKDIR /app

#ENV GO111MODULE=on \
#    GOPROXY=https://goproxy.cn,direct

#COPY . .
#RUN CGO_ENABLED=0 go build -trimpath -ldflags="-s -w -extldflags=-static" -o /app/watchvuln .


FROM alpine:3

RUN apk add --update tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata && \
    rm -rf /var/cache/apk/*

#WORKDIR /app

#COPY --from=builder /app/watchvuln /app/watchvuln

ENV DINGDING_ACCESS_TOKEN="" DINGDING_SECRET="" WECHATWORK_KEY="" BARK_URL="" GRPC_ADDRESS="" INTERVAL=30m
ENTRYPOINT ["/app/watchvuln"]