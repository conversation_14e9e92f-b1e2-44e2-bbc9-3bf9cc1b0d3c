db_conn: sqlite3://vuln_v3.sqlite3
interval: 30m
enable_cve_filter: true
no_github_search: false
no_start_message: false
diff_mode: false
white_keywords: [ ]
black_keywords: [ ]
skip_tls_verify: false
proxy: ""

pusher:
  - type: dingding
    access_token: "9e9988a0950e87d82d721b028bd591b46f1c523a9f8dcb4e0867c0f5fd99ac82"
    sign_secret: "SECdce4038b82e06dcb1bc532c678b2f0fb13a1a26680bcff40b8408195dcb122cc"

  - type: lark
    access_token: ""
    sign_secret: ""

  - type: wechatwork
    key: ""

  - type: lanxin
    domain: ""
    access_token: ""
    sign_secret: ""

  - type: bark
    url: ""

  - type: serverchan
    key: ""

  - type: pushplus
    token: ""

  - type: telegram
    bot_token: ""
    chat_ids: ""

  - type: webhook
    url: ""

  - type: grpc
    address: "localhost:50051"
    timeout: 300
    insecure: true

