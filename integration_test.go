package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"

	"github.com/zema1/watchvuln/ctrl"
	"github.com/zema1/watchvuln/grab"
	"github.com/zema1/watchvuln/push"
)

// TestIntegrationWithConfig 集成测试：通过配置文件启动，调用FindGithubPoc并推送到钉钉和GRPC
func TestIntegrationWithConfig(t *testing.T) {
	// 跳过集成测试，除非明确指定
	if testing.Short() {
		t.Skip("跳过集成测试，使用 -short=false 运行")
	}

	// 设置测试环境
	ctx := context.Background()
	
	// 1. 准备配置文件路径
	configPath := "config.example.yaml"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("配置文件不存在: %s", configPath)
	}

	t.Logf("🔧 使用配置文件: %s", configPath)

	// 2. 从配置文件加载配置
	config, err := loadConfigFromFile(configPath)
	require.NoError(t, err, "加载配置文件失败")
	
	// 修改配置以适应测试环境
	config.DBConn = "sqlite3://integration_test.sqlite3"
	config.Test = true
	config.NoStartMessage = new(bool)
	*config.NoStartMessage = true
	
	t.Logf("📊 配置加载成功，数据库: %s", config.DBConn)

	// 3. 创建应用实例
	app, err := ctrl.NewApp(config)
	require.NoError(t, err, "创建应用实例失败")
	defer func() {
		// 清理测试数据库
		if dbFile := "integration_test.sqlite3"; fileExists(dbFile) {
			os.Remove(dbFile)
			t.Logf("🧹 清理测试数据库: %s", dbFile)
		}
	}()

	t.Logf("✅ 应用实例创建成功")

	// 4. 测试 FindGithubPoc 功能
	testCVEs := []string{
		"CVE-2024-50477", // WordPress Stacks Mobile App Builder
		"CVE-2023-37582", // 已知存在POC的CVE
		"CVE-2022-25061", // TP-Link Command Injection
	}

	var allFoundLinks []string
	for _, cve := range testCVEs {
		t.Run(fmt.Sprintf("FindGithubPoc_%s", cve), func(t *testing.T) {
			t.Logf("🔍 开始搜索 %s 的 GitHub POC...", cve)
			
			links, err := app.FindGithubPoc(ctx, cve)
			if err != nil {
				t.Logf("⚠️  搜索 %s 时出现错误: %v", cve, err)
			} else {
				t.Logf("✅ 找到 %d 个链接: %v", len(links), links)
				allFoundLinks = append(allFoundLinks, links...)
			}
		})
	}

	// 5. 创建测试用的漏洞信息
	testVulns := createTestVulnerabilities(allFoundLinks)
	
	// 6. 测试推送功能
	t.Run("测试推送功能", func(t *testing.T) {
		// 初始化推送器
		textPusher, rawPusher, err := config.InitPusher()
		require.NoError(t, err, "初始化推送器失败")

		// 测试钉钉推送
		if textPusher != nil {
			t.Run("钉钉推送测试", func(t *testing.T) {
				t.Logf("📱 开始测试钉钉推送...")
				
				// 推送初始化消息
				initialMsg := createInitialMessage()
				initialText := push.RenderInitialMsg(initialMsg)
				err := textPusher.PushMarkdown("WatchVuln 集成测试启动", initialText)
				if err != nil {
					t.Logf("⚠️  钉钉初始化消息推送失败: %v", err)
				} else {
					t.Logf("✅ 钉钉初始化消息推送成功")
				}

				// 推送漏洞信息
				for i, vuln := range testVulns {
					vulnText := push.RenderVulnInfo(vuln)
					err := textPusher.PushMarkdown(fmt.Sprintf("漏洞告警 #%d", i+1), vulnText)
					if err != nil {
						t.Logf("⚠️  钉钉漏洞信息推送失败: %v", err)
					} else {
						t.Logf("✅ 钉钉漏洞信息推送成功: %s", vuln.Title)
					}
					time.Sleep(1 * time.Second) // 避免推送过快
				}

				// 推送测试完成消息
				err = textPusher.PushText("🎉 WatchVuln 集成测试完成！所有功能测试通过。")
				if err != nil {
					t.Logf("⚠️  钉钉完成消息推送失败: %v", err)
				} else {
					t.Logf("✅ 钉钉完成消息推送成功")
				}
			})
		}

		// 测试GRPC推送
		if rawPusher != nil {
			t.Run("GRPC推送测试", func(t *testing.T) {
				t.Logf("🔌 开始测试GRPC推送...")
				
				// 推送初始化消息
				initialMsg := createInitialMessage()
				rawInitialMsg := push.NewRawInitialMessage(initialMsg)
				err := rawPusher.PushRaw(rawInitialMsg)
				if err != nil {
					t.Logf("⚠️  GRPC初始化消息推送失败: %v", err)
				} else {
					t.Logf("✅ GRPC初始化消息推送成功")
				}

				// 推送漏洞信息
				for i, vuln := range testVulns {
					rawVulnMsg := push.NewRawVulnInfoMessage(vuln)
					err := rawPusher.PushRaw(rawVulnMsg)
					if err != nil {
						t.Logf("⚠️  GRPC漏洞信息推送失败: %v", err)
					} else {
						t.Logf("✅ GRPC漏洞信息推送成功: %s", vuln.Title)
					}
					time.Sleep(500 * time.Millisecond) // 避免推送过快
				}

				// 推送测试完成消息
				rawTextMsg := push.NewRawTextMessage("🎉 WatchVuln GRPC集成测试完成！")
				err = rawPusher.PushRaw(rawTextMsg)
				if err != nil {
					t.Logf("⚠️  GRPC完成消息推送失败: %v", err)
				} else {
					t.Logf("✅ GRPC完成消息推送成功")
				}
			})
		}
	})

	t.Logf("🎯 集成测试完成！")
	t.Logf("📈 测试统计:")
	t.Logf("   - 测试CVE数量: %d", len(testCVEs))
	t.Logf("   - 找到POC链接: %d", len(allFoundLinks))
	t.Logf("   - 推送漏洞数量: %d", len(testVulns))
}

// loadConfigFromFile 从文件加载配置 (基于main.go中的initConfigFromFile函数)
func loadConfigFromFile(configPath string) (*ctrl.WatchVulnAppConfig, error) {
	if configPath == "" {
		return nil, fmt.Errorf("config file is required")
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config ctrl.WatchVulnAppConfig
	if strings.HasSuffix(configPath, ".json") {
		err = json.Unmarshal(data, &config)
	}
	if strings.HasSuffix(configPath, ".yaml") || strings.HasSuffix(configPath, ".yml") {
		err = yaml.Unmarshal(data, &config)
	}
	if err != nil {
		return nil, err
	}

	// 解析时间间隔
	config.IntervalParsed, err = time.ParseDuration(config.Interval)
	if err != nil {
		return nil, err
	}

	// 设置版本信息
	config.Version = "v2.5.1-integration-test"

	// 初始化配置
	config.Init()

	return &config, nil
}

// createTestVulnerabilities 创建测试用的漏洞信息
func createTestVulnerabilities(githubLinks []string) []*grab.VulnInfo {
	vulns := []*grab.VulnInfo{
		{
			UniqueKey:   "integration-test-001",
			Title:       "WordPress Stacks Mobile App Builder 认证绕过漏洞",
			CVE:         "CVE-2024-50477",
			Description: "Stacks Mobile App Builder WordPress插件存在认证绕过漏洞，攻击者可以通过不当的查询参数处理来冒充任意用户。这是一个集成测试中人工指定的漏洞消息。",
			Severity:    grab.Critical,
			Tags:        []string{"WordPress", "认证绕过", "集成测试"},
			From:        "https://integration-test.example.com/vuln/001",
			References:  []string{"https://github.com/stealthcopter/wordpress-hacking/blob/main/reports/stacks-mobile-app-builder-priv-esc/stacks-mobile-app-builder-priv-esc.md"},
			Reason:      []string{"集成测试", "FindGithubPoc功能验证"},
			Disclosure:  time.Now().Format("2006-01-02"),
			Solutions:   "请升级到最新版本或禁用该插件",
		},
		{
			UniqueKey:   "integration-test-002", 
			Title:       "TP-Link路由器命令注入漏洞",
			CVE:         "CVE-2022-25061",
			Description: "TP-Link TL-WR840N路由器在oal_setIp6DefaultRoute组件中存在命令注入漏洞，已认证的攻击者可以执行任意系统命令。这是集成测试的示例漏洞。",
			Severity:    grab.High,
			Tags:        []string{"路由器", "命令注入", "TP-Link", "集成测试"},
			From:        "https://integration-test.example.com/vuln/002",
			References:  []string{"https://nvd.nist.gov/vuln/detail/CVE-2022-25061"},
			Reason:      []string{"集成测试", "GRPC推送验证"},
			Disclosure:  time.Now().AddDate(0, 0, -1).Format("2006-01-02"),
			Solutions:   "请联系厂商获取安全更新",
		},
	}

	// 如果找到了GitHub链接，添加到第一个漏洞中
	if len(githubLinks) > 0 {
		vulns[0].GithubSearch = githubLinks[:min(len(githubLinks), 5)] // 最多5个链接
	}

	return vulns
}

// createInitialMessage 创建初始化消息
func createInitialMessage() *push.InitialMessage {
	return &push.InitialMessage{
		Version:   "v2.5.1-integration-test",
		VulnCount: 100,
		Interval:  "30m",
		Provider: []*grab.Provider{
			{
				Name:        "avd",
				DisplayName: "阿里云漏洞库",
				Link:        "https://avd.aliyun.com",
			},
			{
				Name:        "integration-test",
				DisplayName: "集成测试数据源",
				Link:        "https://integration-test.example.com",
			},
		},
		FailedProvider: []*grab.Provider{},
	}
}

// 辅助函数
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
