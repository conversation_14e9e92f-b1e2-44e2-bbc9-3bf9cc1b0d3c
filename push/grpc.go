package push

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/kataras/golog"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/zema1/watchvuln/grab"
	pb "github.com/zema1/watchvuln/proto"
)

var _ = RawPusher(&GrpcPusher{})

const TypeGrpc = "grpc"

type GrpcConfig struct {
	Type    string `json:"type" yaml:"type"`
	Address string `yaml:"address" json:"address"`
}

type GrpcPusher struct {
	config  *GrpcConfig
	log     *golog.Logger
	conn    *grpc.ClientConn
	client  pb.VulnPusherClient
	timeout int
}

func NewGrpcPusher(config *GrpcConfig) (RawPusher, error) {

	// 建立gRPC连接
	var opts []grpc.DialOption
	opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))

	conn, err := grpc.Dial(config.Address, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to gRPC server: %w", err)
	}

	client := pb.NewVulnPusherClient(conn)

	return &GrpcPusher{
		config:  config,
		log:     golog.Child("[pusher-grpc]"),
		conn:    conn,
		client:  client,
		timeout: 300, // 默认5分钟超时
	}, nil
}

func (g *GrpcPusher) PushRaw(r *RawMessage) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(g.timeout)*time.Second)
	defer cancel()

	g.log.Infof("sending grpc data type: %s", r.Type)

	switch r.Type {
	case RawMessageTypeVulnInfo:
		return g.pushVulnInfo(ctx, r)
	case RawMessageTypeInitial:
		return g.pushInitialMessage(ctx, r)
	case RawMessageTypeText:
		return g.pushTextMessage(ctx, r)
	default:
		g.log.Warnf("unknown message type: %s", r.Type)
		return nil
	}
}

func (g *GrpcPusher) pushVulnInfo(ctx context.Context, r *RawMessage) error {
	// 将RawMessage的Content转换为grab.VulnInfo
	contentBytes, err := json.Marshal(r.Content)
	if err != nil {
		return fmt.Errorf("failed to marshal content: %w", err)
	}

	var vulnInfo grab.VulnInfo
	if err := json.Unmarshal(contentBytes, &vulnInfo); err != nil {
		return fmt.Errorf("failed to unmarshal vuln info: %w", err)
	}

	// 转换为protobuf消息
	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			UniqueKey:    vulnInfo.UniqueKey,
			Title:        vulnInfo.Title,
			Description:  vulnInfo.Description,
			Severity:     string(vulnInfo.Severity),
			Cve:          vulnInfo.CVE,
			Disclosure:   vulnInfo.Disclosure,
			Solutions:    vulnInfo.Solutions,
			GithubSearch: vulnInfo.GithubSearch,
			References:   vulnInfo.References,
			Tags:         vulnInfo.Tags,
			From:         vulnInfo.From,
			Reason:       vulnInfo.Reason,
		},
		Timestamp: time.Now().Unix(),
	}

	_, err = g.client.PushVuln(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to push vuln info: %w", err)
	}

	g.log.Infof("successfully pushed vuln info: %s", vulnInfo.Title)
	return nil
}

func (g *GrpcPusher) pushInitialMessage(ctx context.Context, r *RawMessage) error {
	contentBytes, err := json.Marshal(r.Content)
	if err != nil {
		return fmt.Errorf("failed to marshal content: %w", err)
	}

	// 使用msg.go中定义的InitialMessage类型
	var msgInitial InitialMessage
	if err := json.Unmarshal(contentBytes, &msgInitial); err != nil {
		return fmt.Errorf("failed to unmarshal initial message: %w", err)
	}

	// 转换grab.Provider到protobuf Provider
	pbProviders := make([]*pb.Provider, len(msgInitial.Provider))
	for i, p := range msgInitial.Provider {
		pbProviders[i] = &pb.Provider{
			Name:        p.Name,
			DisplayName: p.DisplayName,
			Link:        p.Link,
		}
	}

	pbFailedProviders := make([]*pb.Provider, len(msgInitial.FailedProvider))
	for i, p := range msgInitial.FailedProvider {
		pbFailedProviders[i] = &pb.Provider{
			Name:        p.Name,
			DisplayName: p.DisplayName,
			Link:        p.Link,
		}
	}

	// 创建protobuf InitialMessage
	pbInitialMsg := &pb.InitialMessage{
		Version:        msgInitial.Version,
		VulnCount:      int32(msgInitial.VulnCount),
		Interval:       msgInitial.Interval,
		Provider:       pbProviders,
		FailedProvider: pbFailedProviders,
	}

	req := &pb.PushInitialRequest{
		InitialMessage: pbInitialMsg,
		Timestamp:      time.Now().Unix(),
	}

	_, err = g.client.PushInitial(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to push initial message: %w", err)
	}

	g.log.Infof("successfully pushed initial message")
	return nil
}

func (g *GrpcPusher) pushTextMessage(ctx context.Context, r *RawMessage) error {
	// 对于文本消息，Content通常是字符串
	var message string
	if str, ok := r.Content.(string); ok {
		message = str
	} else {
		// 如果不是字符串，尝试JSON序列化
		contentBytes, err := json.Marshal(r.Content)
		if err != nil {
			return fmt.Errorf("failed to marshal content: %w", err)
		}
		message = string(contentBytes)
	}

	req := &pb.PushTextRequest{
		Message:   message,
		Timestamp: time.Now().Unix(),
	}

	_, err := g.client.PushText(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to push text message: %w", err)
	}

	g.log.Infof("successfully pushed text message")
	return nil
}

func (g *GrpcPusher) Close() error {
	if g.conn != nil {
		return g.conn.Close()
	}
	return nil
}
