package push

import (
	"context"
	"encoding/json"
	"net"
	"testing"
	"time"

	"github.com/kataras/golog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	"github.com/zema1/watchvuln/grab"
	pb "github.com/zema1/watchvuln/proto"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

// MockVulnPusherServer 模拟gRPC服务器
type MockVulnPusherServer struct {
	pb.UnimplementedVulnPusherServer

	// 记录接收到的消息
	ReceivedVulns    []*pb.PushVulnRequest
	ReceivedInitials []*pb.PushInitialRequest
	ReceivedTexts    []*pb.PushTextRequest

	// 控制响应
	ShouldFail  bool
	FailMessage string
}

func (m *MockVulnPusherServer) PushVuln(ctx context.Context, req *pb.PushVulnRequest) (*pb.PushVulnResponse, error) {
	m.ReceivedVulns = append(m.ReceivedVulns, req)

	if m.ShouldFail {
		return &pb.PushVulnResponse{
			Success: false,
			Message: m.FailMessage,
		}, nil
	}

	return &pb.PushVulnResponse{
		Success: true,
		Message: "success",
	}, nil
}

func (m *MockVulnPusherServer) PushInitial(ctx context.Context, req *pb.PushInitialRequest) (*pb.PushInitialResponse, error) {
	m.ReceivedInitials = append(m.ReceivedInitials, req)

	if m.ShouldFail {
		return &pb.PushInitialResponse{
			Success: false,
			Message: m.FailMessage,
		}, nil
	}

	return &pb.PushInitialResponse{
		Success: true,
		Message: "success",
	}, nil
}

func (m *MockVulnPusherServer) PushText(ctx context.Context, req *pb.PushTextRequest) (*pb.PushTextResponse, error) {
	m.ReceivedTexts = append(m.ReceivedTexts, req)

	if m.ShouldFail {
		return &pb.PushTextResponse{
			Success: false,
			Message: m.FailMessage,
		}, nil
	}

	return &pb.PushTextResponse{
		Success: true,
		Message: "success",
	}, nil
}

func (m *MockVulnPusherServer) Reset() {
	m.ReceivedVulns = nil
	m.ReceivedInitials = nil
	m.ReceivedTexts = nil
	m.ShouldFail = false
	m.FailMessage = ""
}

func bufDialer(context.Context, string) (net.Conn, error) {
	return lis.Dial()
}

func setupTestServer() *MockVulnPusherServer {
	lis = bufconn.Listen(bufSize)
	s := grpc.NewServer()
	mockServer := &MockVulnPusherServer{}
	pb.RegisterVulnPusherServer(s, mockServer)

	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()

	return mockServer
}

func createTestGrpcPusher(t *testing.T) *GrpcPusher {
	config := &GrpcConfig{
		Address:  "bufnet",
		Timeout:  10,
		Insecure: true,
	}

	conn, err := grpc.Dial("bufnet",
		grpc.WithContextDialer(bufDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	require.NoError(t, err)

	client := pb.NewVulnPusherClient(conn)

	return &GrpcPusher{
		config: config,
		log:    golog.Child("[test-grpc]"),
		conn:   conn,
		client: client,
	}
}

func TestGrpcPusher_PushVulnInfo(t *testing.T) {
	mockServer := setupTestServer()
	defer mockServer.Reset()

	pusher := createTestGrpcPusher(t)
	defer pusher.Close()

	// 创建测试漏洞信息
	vulnInfo := &grab.VulnInfo{
		UniqueKey:    "test-001",
		Title:        "Test Vulnerability",
		Description:  "This is a test vulnerability",
		Severity:     grab.High,
		CVE:          "CVE-2024-0001",
		Disclosure:   "2024-01-01",
		Solutions:    "Update to latest version",
		GithubSearch: []string{"https://github.com/test/repo"},
		References:   []string{"https://example.com/vuln"},
		Tags:         []string{"RCE", "Critical"},
		From:         "https://test.com/vuln/001",
		Reason:       []string{"新漏洞创建"},
	}

	rawMsg := NewRawVulnInfoMessage(vulnInfo)

	// 测试推送
	err := pusher.PushRaw(rawMsg)
	assert.NoError(t, err)

	// 验证服务器接收到消息
	require.Len(t, mockServer.ReceivedVulns, 1)
	received := mockServer.ReceivedVulns[0]

	assert.Equal(t, vulnInfo.UniqueKey, received.VulnInfo.UniqueKey)
	assert.Equal(t, vulnInfo.Title, received.VulnInfo.Title)
	assert.Equal(t, vulnInfo.CVE, received.VulnInfo.Cve)
	assert.Equal(t, string(vulnInfo.Severity), received.VulnInfo.Severity)
	assert.Equal(t, vulnInfo.Tags, received.VulnInfo.Tags)
	assert.True(t, received.Timestamp > 0)
	assert.True(t, time.Unix(received.Timestamp, 0).Before(time.Now().Add(time.Second)))
}

func TestGrpcPusher_PushInitialMessage(t *testing.T) {
	mockServer := setupTestServer()
	defer mockServer.Reset()

	pusher := createTestGrpcPusher(t)
	defer pusher.Close()

	// 创建测试初始化消息
	initialMsg := &InitialMessage{
		Version:   "v1.0.0",
		VulnCount: 100,
		Interval:  "30m",
		Provider: []*grab.Provider{
			{Name: "test", DisplayName: "Test Provider", Link: "https://test.com"},
		},
		FailedProvider: []*grab.Provider{
			{Name: "failed", DisplayName: "Failed Provider", Link: "https://failed.com"},
		},
	}

	rawMsg := NewRawInitialMessage(initialMsg)

	// 测试推送
	err := pusher.PushRaw(rawMsg)
	assert.NoError(t, err)

	// 验证服务器接收到消息
	require.Len(t, mockServer.ReceivedInitials, 1)
	received := mockServer.ReceivedInitials[0]

	assert.Equal(t, initialMsg.Version, received.InitialMessage.Version)
	assert.Equal(t, int32(initialMsg.VulnCount), received.InitialMessage.VulnCount)
	assert.Equal(t, initialMsg.Interval, received.InitialMessage.Interval)
	assert.Len(t, received.InitialMessage.Provider, 1)
	assert.Len(t, received.InitialMessage.FailedProvider, 1)
}

func TestGrpcPusher_PushTextMessage(t *testing.T) {
	mockServer := setupTestServer()
	defer mockServer.Reset()

	pusher := createTestGrpcPusher(t)
	defer pusher.Close()

	// 测试字符串消息
	textMsg := "WatchVuln 进程退出"
	rawMsg := NewRawTextMessage(textMsg)

	err := pusher.PushRaw(rawMsg)
	assert.NoError(t, err)

	// 验证服务器接收到消息
	require.Len(t, mockServer.ReceivedTexts, 1)
	received := mockServer.ReceivedTexts[0]

	// 解析JSON内容
	var textMessage TextMessage
	err = json.Unmarshal([]byte(received.Message), &textMessage)
	assert.NoError(t, err)
	assert.Equal(t, textMsg, textMessage.Message)
}

func TestGrpcPusher_ErrorHandling(t *testing.T) {
	mockServer := setupTestServer()
	defer mockServer.Reset()

	// 设置服务器返回错误
	mockServer.ShouldFail = true
	mockServer.FailMessage = "Server error"

	pusher := createTestGrpcPusher(t)
	defer pusher.Close()

	vulnInfo := &grab.VulnInfo{
		UniqueKey: "test-error",
		Title:     "Error Test",
	}

	rawMsg := NewRawVulnInfoMessage(vulnInfo)

	// 测试推送应该成功（服务器返回错误响应但不抛异常）
	err := pusher.PushRaw(rawMsg)
	assert.NoError(t, err)

	// 验证服务器接收到消息
	require.Len(t, mockServer.ReceivedVulns, 1)
}

func TestGrpcPusher_Timeout(t *testing.T) {
	// 创建超时配置
	config := &GrpcConfig{
		Address:  "bufnet",
		Timeout:  1, // 1秒超时
		Insecure: true,
	}

	conn, err := grpc.Dial("bufnet",
		grpc.WithContextDialer(bufDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	require.NoError(t, err)

	client := pb.NewVulnPusherClient(conn)

	pusher := &GrpcPusher{
		config: config,
		log:    golog.Child("[test-timeout]"),
		conn:   conn,
		client: client,
	}
	defer pusher.Close()

	vulnInfo := &grab.VulnInfo{
		UniqueKey: "test-timeout",
		Title:     "Timeout Test",
	}

	rawMsg := NewRawVulnInfoMessage(vulnInfo)

	// 测试推送（应该在超时时间内完成）
	err = pusher.PushRaw(rawMsg)
	assert.NoError(t, err)
}

func TestGrpcPusher_UnknownMessageType(t *testing.T) {
	mockServer := setupTestServer()
	defer mockServer.Reset()

	pusher := createTestGrpcPusher(t)
	defer pusher.Close()

	// 创建未知类型的消息
	rawMsg := &RawMessage{
		Type:    "unknown-type",
		Content: "test content",
	}

	// 测试推送未知类型消息（应该被忽略）
	err := pusher.PushRaw(rawMsg)
	assert.NoError(t, err)

	// 验证服务器没有接收到任何消息
	assert.Len(t, mockServer.ReceivedVulns, 0)
	assert.Len(t, mockServer.ReceivedInitials, 0)
	assert.Len(t, mockServer.ReceivedTexts, 0)
}

// TestGrpcPusher_RealServer 测试推送到真实的gRPC服务器 (localhost:50051)
// 注意：这个测试需要先启动POC-Finder服务器
func TestGrpcPusher_RealServer(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过真实服务器测试（使用 -short 标志）")
	}

	// 创建真实的gRPC推送器配置
	config := &GrpcConfig{
		Address:  "localhost:50051",
		Timeout:  30,
		Insecure: true,
	}

	// 创建gRPC推送器
	rawPusher, err := NewGrpcPusher(config)
	if err != nil {
		t.Skipf("无法连接到localhost:50051，请先启动POC-Finder服务器: %v", err)
	}
	grpcPusher := rawPusher.(*GrpcPusher)
	defer grpcPusher.Close()

	t.Run("推送初始化消息", func(t *testing.T) {
		initialMsg := &InitialMessage{
			Version:   "v1.0.0-test",
			VulnCount: 150,
			Interval:  "30m",
			Provider: []*grab.Provider{
				{
					Name:        "avd",
					DisplayName: "阿里云漏洞库",
					Link:        "https://avd.aliyun.com",
				},
				{
					Name:        "ti",
					DisplayName: "奇安信威胁情报中心",
					Link:        "https://ti.qianxin.com",
				},
				{
					Name:        "oscs",
					DisplayName: "OSCS开源安全情报预警",
					Link:        "https://www.oscs1024.com",
				},
			},
			FailedProvider: []*grab.Provider{
				{
					Name:        "seebug",
					DisplayName: "知道创宇Seebug漏洞库",
					Link:        "https://www.seebug.org",
				},
			},
		}

		rawMsg := NewRawInitialMessage(initialMsg)
		err := grpcPusher.PushRaw(rawMsg)
		assert.NoError(t, err, "初始化消息推送应该成功")

		t.Logf("✅ 成功推送初始化消息到 localhost:50051")
	})

	t.Run("推送严重漏洞信息", func(t *testing.T) {
		criticalVuln := &grab.VulnInfo{
			UniqueKey:   "real-test-critical-001",
			Title:       "Apache Struts2 远程代码执行漏洞",
			Description: "Apache Struts2框架存在远程代码执行漏洞，攻击者可以通过构造恶意请求执行任意代码，获取服务器控制权。该漏洞影响范围广泛，建议立即修复。",
			Severity:    grab.Critical,
			CVE:         "CVE-2024-TEST001",
			Disclosure:  "2024-01-15",
			Solutions:   "1. 立即升级到最新版本\n2. 如无法升级，请部署WAF规则\n3. 限制网络访问权限",
			GithubSearch: []string{
				"https://github.com/search?q=CVE-2024-TEST001",
				"https://github.com/vulhub/vulhub/tree/master/struts2",
			},
			References: []string{
				"https://struts.apache.org/security/",
				"https://nvd.nist.gov/vuln/detail/CVE-2024-TEST001",
				"https://www.exploit-db.com/exploits/12345",
			},
			Tags: []string{
				"RCE",
				"POC公开",
				"在野利用",
				"nuclei-templates",
				"Apache",
				"Java",
			},
			From:   "https://ti.qianxin.com/vulnerability/detail/CVE-2024-TEST001",
			Reason: []string{"严重漏洞", "POC公开", "影响范围广"},
		}

		rawMsg := NewRawVulnInfoMessage(criticalVuln)
		err := grpcPusher.PushRaw(rawMsg)
		assert.NoError(t, err, "严重漏洞推送应该成功")

		t.Logf("✅ 成功推送严重漏洞: %s", criticalVuln.Title)
	})

	t.Run("推送高危漏洞信息", func(t *testing.T) {
		highVuln := &grab.VulnInfo{
			UniqueKey:   "real-test-high-002",
			Title:       "Spring Boot Actuator 信息泄露漏洞",
			Description: "Spring Boot Actuator端点配置不当导致敏感信息泄露，攻击者可获取应用配置、环境变量、数据库连接信息等敏感数据。",
			Severity:    grab.High,
			CVE:         "CVE-2024-TEST002",
			Disclosure:  "2024-01-20",
			Solutions:   "1. 禁用不必要的Actuator端点\n2. 配置访问控制\n3. 使用Spring Security保护端点",
			GithubSearch: []string{
				"https://github.com/search?q=spring+boot+actuator+vulnerability",
			},
			References: []string{
				"https://spring.io/security/cve-2024-test002",
				"https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html#actuator.endpoints.security",
			},
			Tags: []string{
				"信息泄露",
				"Spring Boot",
				"配置错误",
				"Web安全",
			},
			From:   "https://avd.aliyun.com/detail?id=AVD-2024-TEST002",
			Reason: []string{"高危漏洞", "配置问题"},
		}

		rawMsg := NewRawVulnInfoMessage(highVuln)
		err := grpcPusher.PushRaw(rawMsg)
		assert.NoError(t, err, "高危漏洞推送应该成功")

		t.Logf("✅ 成功推送高危漏洞: %s", highVuln.Title)
	})

	t.Run("推送中危漏洞信息", func(t *testing.T) {
		mediumVuln := &grab.VulnInfo{
			UniqueKey:   "real-test-medium-003",
			Title:       "Nginx HTTP请求走私漏洞",
			Description: "Nginx在处理HTTP请求时存在请求走私漏洞，攻击者可能绕过安全控制或进行缓存投毒攻击。",
			Severity:    grab.Medium,
			CVE:         "CVE-2024-TEST003",
			Disclosure:  "2024-01-25",
			Solutions:   "1. 升级Nginx到最新版本\n2. 检查代理配置\n3. 启用严格的HTTP解析",
			References: []string{
				"https://nginx.org/en/security_advisories.html",
				"https://portswigger.net/research/http-request-smuggling",
			},
			Tags: []string{
				"HTTP请求走私",
				"Nginx",
				"代理绕过",
				"Web安全",
			},
			From:   "https://www.oscs1024.com/hd/MPS-2024-TEST003",
			Reason: []string{"中危漏洞", "代理相关"},
		}

		rawMsg := NewRawVulnInfoMessage(mediumVuln)
		err := grpcPusher.PushRaw(rawMsg)
		assert.NoError(t, err, "中危漏洞推送应该成功")

		t.Logf("✅ 成功推送中危漏洞: %s", mediumVuln.Title)
	})

	t.Run("推送包含nuclei-templates标签的漏洞", func(t *testing.T) {
		nucleiVuln := &grab.VulnInfo{
			UniqueKey:   "real-test-nuclei-004",
			Title:       "WordPress Plugin XSS漏洞",
			Description: "WordPress某插件存在存储型XSS漏洞，已有nuclei检测模板可用于批量检测。",
			Severity:    grab.Medium,
			CVE:         "CVE-2024-TEST004",
			Disclosure:  "2024-01-30",
			Solutions:   "1. 更新插件到最新版本\n2. 如无更新，建议禁用该插件\n3. 部署XSS防护规则",
			GithubSearch: []string{
				"https://github.com/projectdiscovery/nuclei-templates/blob/main/cves/2024/CVE-2024-TEST004.yaml",
			},
			References: []string{
				"https://wordpress.org/plugins/vulnerable-plugin/",
				"https://wpscan.com/vulnerability/CVE-2024-TEST004",
			},
			Tags: []string{
				"XSS",
				"WordPress",
				"Plugin",
				"nuclei-templates",
				"Web安全",
			},
			From:   "https://ti.qianxin.com/vulnerability/detail/CVE-2024-TEST004",
			Reason: []string{"nuclei模板可用", "WordPress漏洞"},
		}

		rawMsg := NewRawVulnInfoMessage(nucleiVuln)
		err := grpcPusher.PushRaw(rawMsg)
		assert.NoError(t, err, "nuclei漏洞推送应该成功")

		t.Logf("✅ 成功推送nuclei模板漏洞: %s", nucleiVuln.Title)
	})

	t.Run("批量推送多个漏洞", func(t *testing.T) {
		vulns := []*grab.VulnInfo{
			{
				UniqueKey:   "real-test-batch-001",
				Title:       "Redis未授权访问漏洞",
				Description: "Redis服务器配置不当，允许未授权访问，可能导致数据泄露或服务器被控制。",
				Severity:    grab.High,
				CVE:         "CVE-2024-BATCH001",
				Tags:        []string{"未授权访问", "Redis", "数据库"},
				From:        "https://avd.aliyun.com/detail?id=AVD-2024-BATCH001",
				Reason:      []string{"配置问题", "高危漏洞"},
			},
			{
				UniqueKey:   "real-test-batch-002",
				Title:       "MySQL SQL注入漏洞",
				Description: "应用程序在处理用户输入时存在SQL注入漏洞，攻击者可执行任意SQL语句。",
				Severity:    grab.High,
				CVE:         "CVE-2024-BATCH002",
				Tags:        []string{"SQL注入", "MySQL", "Web安全"},
				From:        "https://www.oscs1024.com/hd/MPS-2024-BATCH002",
				Reason:      []string{"SQL注入", "数据库安全"},
			},
			{
				UniqueKey:   "real-test-batch-003",
				Title:       "Docker容器逃逸漏洞",
				Description: "Docker运行时存在容器逃逸漏洞，攻击者可能突破容器限制访问宿主机。",
				Severity:    grab.Critical,
				CVE:         "CVE-2024-BATCH003",
				Tags:        []string{"容器逃逸", "Docker", "权限提升"},
				From:        "https://ti.qianxin.com/vulnerability/detail/CVE-2024-BATCH003",
				Reason:      []string{"容器安全", "严重漏洞"},
			},
		}

		successCount := 0
		for i, vuln := range vulns {
			rawMsg := NewRawVulnInfoMessage(vuln)
			err := grpcPusher.PushRaw(rawMsg)
			if err != nil {
				t.Errorf("批量推送第%d个漏洞失败: %v", i+1, err)
			} else {
				successCount++
				t.Logf("✅ 成功推送批量漏洞 %d/%d: %s", i+1, len(vulns), vuln.Title)
			}
		}

		assert.Equal(t, len(vulns), successCount, "所有批量漏洞都应该推送成功")
		t.Logf("✅ 批量推送完成: %d/%d 个漏洞推送成功", successCount, len(vulns))
	})

	t.Run("推送文本状态消息", func(t *testing.T) {
		statusMessages := []string{
			"WatchVuln 测试模式启动",
			"开始检查漏洞更新",
			"发现新漏洞 8 个",
			"POC搜索完成",
			"测试推送完成",
		}

		for i, message := range statusMessages {
			rawMsg := NewRawTextMessage(message)
			err := grpcPusher.PushRaw(rawMsg)
			assert.NoError(t, err, "文本消息推送应该成功")
			t.Logf("✅ 成功推送状态消息 %d/%d: %s", i+1, len(statusMessages), message)
		}
	})

	t.Logf("🎉 所有真实服务器测试完成！请检查POC-Finder服务器日志确认数据接收情况。")
}
