# WatchVuln + POC-Finder 简化集成部署
# 这是一个简化版本，适合快速部署和测试
#
# 使用方法：
# 1. 确保目录下有必要的文件（Dockerfile, config.toml, vulnlist.txt）
# 2. 配置 .env 文件或直接修改下面的环境变量
# 3. 运行：docker-compose -f docker-compose-simple.yaml up -d

networks:
  intelli-sec:
    driver: bridge

services:
  # WatchVuln 数据库
  watchvuln-db:
    image: postgres:14.4-alpine
    container_name: watchvuln-db
    restart: always
    environment:
      POSTGRES_DB: watchvuln
      POSTGRES_USER: watchvuln
      POSTGRES_PASSWORD: watchvuln
    volumes:
      - "/opt/watchvuln/data/watchvuln-db:/var/lib/postgresql/data"
    networks:
      - intelli-sec

  # POC-Finder 数据库
  poc-finder-db:
    image: postgres:14.4-alpine
    container_name: poc-finder-db
    restart: always
    environment:
      POSTGRES_DB: poc_finder_db
      POSTGRES_USER: poc_finder
      POSTGRES_PASSWORD: poc_finder
    volumes:
      - "/opt/poc-finder/initdb:/var/lib/postgresql/data"
    networks:
      - intelli-sec

  # POC-Finder 服务（先启动，作为 gRPC 服务器）
  poc-finder:
    build:
      context: .
      dockerfile: poc_finder_dockerfile
    image: poc-finder:latest
    container_name: poc-finder
    restart: always
    command: ["daemon"]
    #    environment:
    #      DATABASE_URL: ***************************************************/poc-finder-db
    #      LOG_LEVEL: info
    volumes:
      - "./poc-finder:/app/poc-finder"
      - "./vulnlist.txt:/app/vulnlist.txt"
      - "./config.toml:/app/config.toml"
    depends_on:
      - poc-finder-db
    networks:
      - intelli-sec

  # WatchVuln 服务（后启动，作为 gRPC 客户端）
  watchvuln:
    build:
      context: .
      dockerfile: watchvuln_dockerfile
    image: watchvuln:latest
    container_name: watchvuln
    restart: always
    environment:
      # 数据库配置
      DB_CONN: ************************************************/watchvuln

      # 基础配置
      INTERVAL: ${INTERVAL:-30m}
      ENABLE_CVE_FILTER: ${ENABLE_CVE_FILTER:-true}
      NO_START_MESSAGE: ${NO_START_MESSAGE:-false}

      # gRPC 推送到 POC-Finder
      GRPC_ADDRESS: poc-finder:50051

      # 钉钉推送
      DINGDING_ACCESS_TOKEN: ${DINGDING_ACCESS_TOKEN}
      DINGDING_SECRET: ${DINGDING_SECRET}
    volumes:
      - "./watchvuln:/app/watchvuln"  # 映射可执行的二进制程序
    depends_on:
      - watchvuln-db
      - poc-finder
    networks:
      - intelli-sec