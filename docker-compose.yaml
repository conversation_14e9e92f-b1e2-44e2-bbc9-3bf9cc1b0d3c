services:
  watchvuln:
    build: .
    restart: always
    image: watchvuln:v0.0.1
    environment:
      DINGDING_ACCESS_TOKEN: ${DINGDING_ACCESS_TOKEN}
      DINGDING_SECRET: ${DINGDING_SECRET}
      DB_CONN: ********************************************/watchvuln
      INTERVAL: 30m
    volumes:
      - "./watchvuln:/app/watchvuln"
    depends_on:
      - postgres

  postgres:
    restart: always
    image: postgres:14.4-alpine
    ports:
      - "127.0.0.1:5432:5432"
    environment:
      POSTGRES_DB: watchvuln
      POSTGRES_USER: watchvuln
      POSTGRES_PASSWORD: watchvuln
    volumes:
      - "postgres-data:/var/lib/postgresql/data"

volumes:
  postgres-data: {}